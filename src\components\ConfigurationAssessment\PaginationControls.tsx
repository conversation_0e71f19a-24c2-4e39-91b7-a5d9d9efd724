import React, { memo } from 'react';
import './PaginationControls.css';

/**
 * Props for the PaginationControls component
 */
interface PaginationControlsProps {
  /**
   * Current page number (1-based)
   */
  currentPage: number;
  
  /**
   * Total number of pages
   */
  totalPages: number;
  
  /**
   * Function to navigate to a specific page
   */
  onPageChange: (page: number) => void;
  
  /**
   * Function to navigate to the first page
   */
  onFirstPage: () => void;
  
  /**
   * Function to navigate to the previous page
   */
  onPreviousPage: () => void;
  
  /**
   * Function to navigate to the next page
   */
  onNextPage: () => void;
  
  /**
   * Function to navigate to the last page
   */
  onLastPage: () => void;
  
  /**
   * Whether the current page is the first page
   */
  isFirstPage: boolean;
  
  /**
   * Whether the current page is the last page
   */
  isLastPage: boolean;
  
  /**
   * Whether the data is currently loading
   */
  isLoading?: boolean;
  
  /**
   * Whether there are no results to paginate
   */
  noResults?: boolean;
  
  /**
   * Whether an operation is in progress
   */
  processingOperation?: boolean;
}

/**
 * Pagination controls component for navigating through pages of data
 * Optimized with memoization to prevent unnecessary re-renders
 */
const PaginationControls: React.FC<PaginationControlsProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  onFirstPage,
  onPreviousPage,
  onNextPage,
  onLastPage,
  isFirstPage,
  isLastPage,
  isLoading = false,
  noResults = false,
  processingOperation = false,
}) => {
  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent, action: () => void) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      action();
    }
  };

  // If there are no results, show a message instead of pagination controls
  if (noResults) {
    return null;
  }

  // Determine if navigation is disabled
  const isNavigationDisabled = isLoading || processingOperation;

  return (
    <div 
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '8px',
        padding: '8px 0',
        backgroundColor: 'rgba(10, 14, 23, 0.6)',
        borderTop: '1px solid rgba(0, 229, 255, 0.2)',
        borderBottom: '1px solid rgba(0, 229, 255, 0.2)',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
        position: 'relative',
      }}
      role="navigation"
      aria-label="Pagination"
    >
      {/* Loading indicator */}
      {isLoading && (
        <div
          className="loading-indicator"
          role="progressbar"
          aria-label="Loading"
        />
      )}
      
      {/* First page button */}
      <button
        className="pagination-button"
        style={{
          color: isFirstPage || isNavigationDisabled ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 229, 255, 0.8)',
          cursor: isFirstPage || isNavigationDisabled ? 'not-allowed' : 'pointer',
          opacity: isNavigationDisabled ? 0.7 : 1,
        }}
        onClick={isFirstPage || isNavigationDisabled ? undefined : onFirstPage}
        onKeyDown={(e) => !isFirstPage && !isNavigationDisabled && handleKeyDown(e, onFirstPage)}
        disabled={isFirstPage || isNavigationDisabled}
        aria-label="First page"
        aria-disabled={isFirstPage || isNavigationDisabled}
        title="First page"
      >
        ⟪
      </button>

      {/* Previous page button */}
      <button
        className="pagination-button"
        style={{
          color: isFirstPage || isNavigationDisabled ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 229, 255, 0.8)',
          cursor: isFirstPage || isNavigationDisabled ? 'not-allowed' : 'pointer',
          opacity: isNavigationDisabled ? 0.7 : 1,
        }}
        onClick={isFirstPage || isNavigationDisabled ? undefined : onPreviousPage}
        onKeyDown={(e) => !isFirstPage && !isNavigationDisabled && handleKeyDown(e, onPreviousPage)}
        disabled={isFirstPage || isNavigationDisabled}
        aria-label="Previous page"
        aria-disabled={isFirstPage || isNavigationDisabled}
        title="Previous page"
      >
        ⟨
      </button>

      {/* Page indicator */}
      <div
        className="pagination-info"
        aria-live="polite"
        aria-atomic="true"
      >
        <span style={{ color: 'rgba(0, 229, 255, 0.8)' }}>Page</span>
        <span style={{ margin: '0 4px', fontWeight: 'bold' }}>{currentPage}</span>
        <span style={{ color: 'rgba(255, 255, 255, 0.5)' }}>of</span>
        <span style={{ margin: '0 4px', fontWeight: 'bold' }}>{totalPages}</span>
        
        {/* Processing indicator */}
        {processingOperation && !isLoading && (
          <span 
            className="processing-indicator"
            role="status"
            aria-label="Processing"
            title="Processing operation"
          />
        )}
      </div>

      {/* Next page button */}
      <button
        className="pagination-button"
        style={{
          color: isLastPage || isNavigationDisabled ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 229, 255, 0.8)',
          cursor: isLastPage || isNavigationDisabled ? 'not-allowed' : 'pointer',
          opacity: isNavigationDisabled ? 0.7 : 1,
        }}
        onClick={isLastPage || isNavigationDisabled ? undefined : onNextPage}
        onKeyDown={(e) => !isLastPage && !isNavigationDisabled && handleKeyDown(e, onNextPage)}
        disabled={isLastPage || isNavigationDisabled}
        aria-label="Next page"
        aria-disabled={isLastPage || isNavigationDisabled}
        title="Next page"
      >
        ⟩
      </button>

      {/* Last page button */}
      <button
        className="pagination-button"
        style={{
          color: isLastPage || isNavigationDisabled ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 229, 255, 0.8)',
          cursor: isLastPage || isNavigationDisabled ? 'not-allowed' : 'pointer',
          opacity: isNavigationDisabled ? 0.7 : 1,
        }}
        onClick={isLastPage || isNavigationDisabled ? undefined : onLastPage}
        onKeyDown={(e) => !isLastPage && !isNavigationDisabled && handleKeyDown(e, onLastPage)}
        disabled={isLastPage || isNavigationDisabled}
        aria-label="Last page"
        aria-disabled={isLastPage || isNavigationDisabled}
        title="Last page"
      >
        ⟫
      </button>
    </div>
  );
};

// Use memo to prevent unnecessary re-renders
export default memo(PaginationControls);