/**
 * Performance Monitor Component for Configuration Assessment
 * 
 * This component provides a UI for monitoring and comparing performance
 * between the Configuration Assessment and Discover pages.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { usePerformanceMonitoring } from '../../hooks/usePerformanceMonitoring';
import { 
  getPerformanceStats, 
  logAllPerformanceMetrics 
} from '../../utils/performanceTracking';
import { 
  getComparisonResults, 
  logPerformanceComparison, 
  OperationCategory 
} from '../../utils/performanceComparison';
import { logPerformanceReport } from '../../utils/performanceLogger';
import { isPerformanceMonitoringEnabled, setPerformanceMonitoringEnabled } from '../../utils/initPerformanceMonitoring';

interface PerformanceMonitorProps {
  visible?: boolean;
  hidden?: boolean;
}

/**
 * Performance Monitor Component
 */
const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({ visible = false, hidden = false }) => {
  const [isVisible, setIsVisible] = useState(visible);
  const [isEnabled, setIsEnabled] = useState(isPerformanceMonitoringEnabled());
  const [expandedSection, setExpandedSection] = useState<string | null>(null);
  
  // Use performance monitoring hook
  const performance = usePerformanceMonitoring('PerformanceMonitor', 'DiscoverPerformanceMonitor');
  
  // Toggle visibility
  const toggleVisibility = useCallback(() => {
    setIsVisible(prev => !prev);
  }, []);
  
  // Toggle enabled state
  const toggleEnabled = useCallback(() => {
    const newState = !isEnabled;
    setIsEnabled(newState);
    setPerformanceMonitoringEnabled(newState);
  }, [isEnabled]);
  
  // Generate performance report
  const generateReport = useCallback(() => {
    logPerformanceReport('Configuration Assessment Performance Report', true, true);
  }, []);
  
  // Toggle section expansion
  const toggleSection = useCallback((section: string) => {
    setExpandedSection(prev => prev === section ? null : section);
  }, []);
  
  // Get performance metrics for key operations
  const [metrics, setMetrics] = useState<{
    filtering: ReturnType<typeof getPerformanceStats>;
    sorting: ReturnType<typeof getPerformanceStats>;
    pagination: ReturnType<typeof getPerformanceStats>;
    rendering: ReturnType<typeof getPerformanceStats>;
  }>({
    filtering: getPerformanceStats('ConfigurationAssessmentContext.filterData', 'filtering'),
    sorting: getPerformanceStats('ConfigurationAssessmentContext.sortData', 'sorting'),
    pagination: getPerformanceStats('useConfigurationEntries.calculatePaginatedEntries', 'pagination'),
    rendering: getPerformanceStats('ConfigurationTable.render', 'rendering')
  });
  
  // Check URL hash for performance monitor activation
  useEffect(() => {
    const checkHash = () => {
      if (window.location.hash === '#perf-monitor') {
        setIsVisible(true);
      }
    };
    
    // Check on initial load
    checkHash();
    
    // Listen for hash changes
    window.addEventListener('hashchange', checkHash);
    return () => {
      window.removeEventListener('hashchange', checkHash);
    };
  }, []);
  
  // Add keyboard shortcut (Ctrl+Shift+P) to toggle visibility
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl+Shift+P to toggle performance monitor
      if (e.ctrlKey && e.shiftKey && e.key === 'P') {
        e.preventDefault();
        toggleVisibility();
      }
    };
    
    // Listen for custom event to show the monitor
    const handleShowMonitor = () => {
      setIsVisible(true);
    };
    
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('show-performance-monitor', handleShowMonitor);
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('show-performance-monitor', handleShowMonitor);
    };
  }, [toggleVisibility]);
  
  // Update metrics periodically
  useEffect(() => {
    if (!isVisible || !isEnabled) return;
    
    const intervalId = setInterval(() => {
      setMetrics({
        filtering: getPerformanceStats('ConfigurationAssessmentContext.filterData', 'filtering'),
        sorting: getPerformanceStats('ConfigurationAssessmentContext.sortData', 'sorting'),
        pagination: getPerformanceStats('useConfigurationEntries.calculatePaginatedEntries', 'pagination'),
        rendering: getPerformanceStats('ConfigurationTable.render', 'rendering')
      });
    }, 1000);
    
    return () => clearInterval(intervalId);
  }, [isVisible, isEnabled]);
  
  // Get comparison results
  const [comparisons, setComparisons] = useState(getComparisonResults());
  
  // Update comparisons periodically
  useEffect(() => {
    if (!isVisible || !isEnabled) return;
    
    const intervalId = setInterval(() => {
      setComparisons(getComparisonResults());
    }, 1000);
    
    return () => clearInterval(intervalId);
  }, [isVisible, isEnabled]);
  
  // If not visible, render nothing or a hidden element
  if (!isVisible) {
    // If hidden is true, don't render anything visible
    if (hidden) {
      return null;
    }
    
    // Otherwise render the toggle button (for backward compatibility)
    return (
      <button
        onClick={toggleVisibility}
        style={{
          position: 'fixed',
          bottom: '10px',
          right: '10px',
          zIndex: 9999,
          padding: '8px 12px',
          backgroundColor: 'rgba(0, 229, 255, 0.2)',
          border: '1px solid rgba(0, 229, 255, 0.5)',
          borderRadius: '4px',
          color: 'rgba(255, 255, 255, 0.8)',
          cursor: 'pointer',
          fontSize: '12px',
          fontFamily: 'monospace',
        }}
      >
        Show Performance Monitor
      </button>
    );
  }
  
  return (
    <div
      style={{
        position: 'fixed',
        bottom: '10px',
        right: '10px',
        zIndex: 9999,
        width: '400px',
        maxHeight: '80vh',
        overflowY: 'auto',
        backgroundColor: 'rgba(10, 14, 23, 0.95)',
        border: '1px solid rgba(0, 229, 255, 0.5)',
        borderRadius: '4px',
        color: 'rgba(255, 255, 255, 0.8)',
        fontFamily: 'monospace',
        fontSize: '12px',
        boxShadow: '0 4px 8px rgba(0, 0, 0, 0.5)',
      }}
    >
      {/* Header */}
      <div
        style={{
          padding: '8px 12px',
          borderBottom: '1px solid rgba(0, 229, 255, 0.3)',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: 'rgba(0, 229, 255, 0.1)',
        }}
      >
        <div style={{ fontWeight: 'bold', color: '#00e5ff' }}>
          Performance Monitor
        </div>
        <div>
          <button
            onClick={toggleEnabled}
            style={{
              marginRight: '8px',
              padding: '4px 8px',
              backgroundColor: isEnabled ? 'rgba(76, 175, 80, 0.2)' : 'rgba(244, 67, 54, 0.2)',
              border: `1px solid ${isEnabled ? 'rgba(76, 175, 80, 0.5)' : 'rgba(244, 67, 54, 0.5)'}`,
              borderRadius: '4px',
              color: isEnabled ? '#4caf50' : '#f44336',
              cursor: 'pointer',
              fontSize: '10px',
            }}
          >
            {isEnabled ? 'Enabled' : 'Disabled'}
          </button>
          <button
            onClick={toggleVisibility}
            style={{
              padding: '4px 8px',
              backgroundColor: 'transparent',
              border: 'none',
              color: 'rgba(255, 255, 255, 0.5)',
              cursor: 'pointer',
              fontSize: '14px',
            }}
          >
            ×
          </button>
        </div>
      </div>
      
      {/* Content */}
      <div style={{ padding: '12px' }}>
        {/* Key Metrics */}
        <div
          style={{
            marginBottom: '12px',
            padding: '8px',
            backgroundColor: 'rgba(0, 229, 255, 0.05)',
            borderRadius: '4px',
            cursor: 'pointer',
          }}
          onClick={() => toggleSection('metrics')}
        >
          <div style={{ fontWeight: 'bold', marginBottom: '4px', display: 'flex', justifyContent: 'space-between' }}>
            <span>Key Metrics</span>
            <span>{expandedSection === 'metrics' ? '▼' : '▶'}</span>
          </div>
          
          {expandedSection === 'metrics' && (
            <div style={{ marginTop: '8px' }}>
              <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                <thead>
                  <tr>
                    <th style={{ textAlign: 'left', padding: '4px', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>Operation</th>
                    <th style={{ textAlign: 'right', padding: '4px', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>Avg (ms)</th>
                    <th style={{ textAlign: 'right', padding: '4px', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>Samples</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td style={{ padding: '4px', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>Filtering</td>
                    <td style={{ 
                      textAlign: 'right', 
                      padding: '4px', 
                      borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                      color: metrics.filtering.avg < 16.67 ? '#4caf50' : metrics.filtering.avg < 50 ? '#ff9800' : '#f44336'
                    }}>
                      {metrics.filtering.avg.toFixed(2)}
                    </td>
                    <td style={{ textAlign: 'right', padding: '4px', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
                      {metrics.filtering.count}
                    </td>
                  </tr>
                  <tr>
                    <td style={{ padding: '4px', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>Sorting</td>
                    <td style={{ 
                      textAlign: 'right', 
                      padding: '4px', 
                      borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                      color: metrics.sorting.avg < 16.67 ? '#4caf50' : metrics.sorting.avg < 50 ? '#ff9800' : '#f44336'
                    }}>
                      {metrics.sorting.avg.toFixed(2)}
                    </td>
                    <td style={{ textAlign: 'right', padding: '4px', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
                      {metrics.sorting.count}
                    </td>
                  </tr>
                  <tr>
                    <td style={{ padding: '4px', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>Pagination</td>
                    <td style={{ 
                      textAlign: 'right', 
                      padding: '4px', 
                      borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                      color: metrics.pagination.avg < 16.67 ? '#4caf50' : metrics.pagination.avg < 50 ? '#ff9800' : '#f44336'
                    }}>
                      {metrics.pagination.avg.toFixed(2)}
                    </td>
                    <td style={{ textAlign: 'right', padding: '4px', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
                      {metrics.pagination.count}
                    </td>
                  </tr>
                  <tr>
                    <td style={{ padding: '4px', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>Rendering</td>
                    <td style={{ 
                      textAlign: 'right', 
                      padding: '4px', 
                      borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                      color: metrics.rendering.avg < 16.67 ? '#4caf50' : metrics.rendering.avg < 50 ? '#ff9800' : '#f44336'
                    }}>
                      {metrics.rendering.avg.toFixed(2)}
                    </td>
                    <td style={{ textAlign: 'right', padding: '4px', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
                      {metrics.rendering.count}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          )}
        </div>
        
        {/* Comparison with Discover */}
        <div
          style={{
            marginBottom: '12px',
            padding: '8px',
            backgroundColor: 'rgba(0, 229, 255, 0.05)',
            borderRadius: '4px',
            cursor: 'pointer',
          }}
          onClick={() => toggleSection('comparison')}
        >
          <div style={{ fontWeight: 'bold', marginBottom: '4px', display: 'flex', justifyContent: 'space-between' }}>
            <span>Comparison with Discover</span>
            <span>{expandedSection === 'comparison' ? '▼' : '▶'}</span>
          </div>
          
          {expandedSection === 'comparison' && (
            <div style={{ marginTop: '8px' }}>
              {comparisons.length === 0 ? (
                <div style={{ color: 'rgba(255, 255, 255, 0.5)', fontStyle: 'italic' }}>
                  No comparison data available
                </div>
              ) : (
                <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                  <thead>
                    <tr>
                      <th style={{ textAlign: 'left', padding: '4px', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>Operation</th>
                      <th style={{ textAlign: 'right', padding: '4px', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>Config (ms)</th>
                      <th style={{ textAlign: 'right', padding: '4px', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>Discover (ms)</th>
                      <th style={{ textAlign: 'right', padding: '4px', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>Diff (%)</th>
                    </tr>
                  </thead>
                  <tbody>
                    {comparisons.slice(0, 5).map((comparison, index) => (
                      <tr key={index}>
                        <td style={{ padding: '4px', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
                          {comparison.operationName.split('.').pop()}
                        </td>
                        <td style={{ textAlign: 'right', padding: '4px', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
                          {comparison.configAssessmentTime.toFixed(2)}
                        </td>
                        <td style={{ textAlign: 'right', padding: '4px', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
                          {comparison.discoverTime.toFixed(2)}
                        </td>
                        <td style={{ 
                          textAlign: 'right', 
                          padding: '4px', 
                          borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                          color: comparison.isFaster ? '#4caf50' : '#f44336'
                        }}>
                          {comparison.isFaster ? '-' : '+'}
                          {Math.abs(comparison.percentageDifference).toFixed(2)}%
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          )}
        </div>
        
        {/* Actions */}
        <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '16px' }}>
          <button
            onClick={generateReport}
            style={{
              padding: '8px 12px',
              backgroundColor: 'rgba(0, 229, 255, 0.2)',
              border: '1px solid rgba(0, 229, 255, 0.5)',
              borderRadius: '4px',
              color: '#00e5ff',
              cursor: 'pointer',
              fontSize: '12px',
            }}
          >
            Generate Report
          </button>
          
          <div style={{ fontSize: '10px', color: 'rgba(255, 255, 255, 0.5)', alignSelf: 'center' }}>
            {`Monitoring ${isEnabled ? 'enabled' : 'disabled'}`}
          </div>
        </div>
        
        {/* Developer Help */}
        <div style={{ 
          marginTop: '12px', 
          padding: '8px', 
          backgroundColor: 'rgba(0, 229, 255, 0.05)',
          borderRadius: '4px',
          fontSize: '10px',
          color: 'rgba(255, 255, 255, 0.6)',
          fontStyle: 'italic'
        }}>
          <div style={{ marginBottom: '4px' }}>Developer Access Methods:</div>
          <ul style={{ margin: '0', paddingLeft: '16px' }}>
            <li>Press <kbd style={{ backgroundColor: 'rgba(255,255,255,0.1)', padding: '1px 3px', borderRadius: '3px' }}>Ctrl+Shift+P</kbd> to toggle this monitor</li>
            <li>Add <code style={{ backgroundColor: 'rgba(255,255,255,0.1)', padding: '1px 3px', borderRadius: '3px' }}>#perf-monitor</code> to the URL to show on page load</li>
            <li>Access via console: <code style={{ backgroundColor: 'rgba(255,255,255,0.1)', padding: '1px 3px', borderRadius: '3px' }}>window.__performanceMonitoring.showMonitor()</code></li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default PerformanceMonitor;