import React, { useState, useCallback } from 'react';

interface TableColumnResizerProps {
  index: number;
  onResize: (index: number, width: number) => void;
}

/**
 * Component for resizing table columns
 */
const TableColumnResizer: React.FC<TableColumnResizerProps> = ({ index, onResize }) => {
  const [isDragging, setIsDragging] = useState(false);
  
  // Start dragging
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
    
    // Add event listeners for mouse move and mouse up
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    
    // Disable text selection while dragging
    document.body.style.userSelect = 'none';
  }, []);
  
  // Handle mouse move during dragging
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging) return;
    
    // Get the parent column element
    const column = (e.target as HTMLElement).closest('[data-field]') as HTMLElement;
    if (!column) return;
    
    // Calculate new width based on mouse position
    const rect = column.getBoundingClientRect();
    const width = e.clientX - rect.left;
    
    // Ensure minimum width
    const minWidth = 50;
    if (width >= minWidth) {
      onResize(index, width);
    }
  }, [isDragging, index, onResize]);
  
  // End dragging
  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    
    // Remove event listeners
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    
    // Re-enable text selection
    document.body.style.userSelect = '';
  }, [handleMouseMove]);
  
  return (
    <div
      role="separator"
      aria-orientation="vertical"
      style={{
        position: 'absolute',
        right: 0,
        top: 0,
        bottom: 0,
        width: '8px',
        cursor: 'col-resize',
        zIndex: 1,
      }}
      onMouseDown={handleMouseDown}
    />
  );
};

export default TableColumnResizer;