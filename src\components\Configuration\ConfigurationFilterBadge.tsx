import React, { useState } from 'react';
import { ConfigurationFilter } from '../../types/configuration';
import { configurationFieldMappings } from '../../utils/configurationAdapters';

interface ConfigurationFilterBadgeProps {
  filter: ConfigurationFilter;
  onRemove: (field: string) => void;
  onEdit?: (filter: ConfigurationFilter) => void;
}

/**
 * Component that displays a filter badge for configuration assessment filters
 * 
 * Requirements: 2.1, 2.2
 */
const ConfigurationFilterBadge: React.FC<ConfigurationFilterBadgeProps> = ({ 
  filter, 
  onRemove,
  onEdit 
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(filter.value as string);
  
  // Get display name for the field
  const fieldPath = filter.field.toString();
  const fieldMapping = configurationFieldMappings[fieldPath as keyof typeof configurationFieldMappings];
  const displayName = fieldMapping ? fieldMapping.displayName : fieldPath;
  
  // Get operator display text
  const operatorText = {
    'eq': 'is',
    'neq': 'is not',
    'contains': 'contains',
    'in': 'is one of',
    'not_in': 'is not one of',
  }[filter.operator] || 'is';
  
  // Format value for display
  const formatValue = (value: any): string => {
    if (value === undefined || value === null) {
      return 'null';
    }
    
    if (Array.isArray(value)) {
      return value.join(', ');
    }
    
    return String(value);
  };
  
  // Handle edit save
  const handleSave = () => {
    if (onEdit) {
      onEdit({
        ...filter,
        value: editValue
      });
    }
    setIsEditing(false);
  };
  
  // Handle key press in edit input
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSave();
    } else if (e.key === 'Escape') {
      setIsEditing(false);
      setEditValue(filter.value as string);
    }
  };
  
  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      background: 'rgba(0, 229, 255, 0.1)',
      border: '1px solid rgba(0, 229, 255, 0.3)',
      borderRadius: '4px',
      padding: '4px 8px',
      fontSize: '12px',
      color: 'white',
    }}>
      <span style={{ fontWeight: 'bold', marginRight: '4px' }}>
        {displayName}
      </span>
      <span style={{ marginRight: '4px', color: 'rgba(255, 255, 255, 0.7)' }}>
        {operatorText}
      </span>
      
      {isEditing ? (
        <input
          type="text"
          value={editValue}
          onChange={(e) => setEditValue(e.target.value)}
          onKeyDown={handleKeyPress}
          onBlur={handleSave}
          autoFocus
          style={{
            background: 'rgba(0, 0, 0, 0.3)',
            border: '1px solid rgba(0, 229, 255, 0.3)',
            borderRadius: '2px',
            color: 'white',
            fontSize: '12px',
            padding: '2px 4px',
            width: '100px',
          }}
        />
      ) : (
        <span 
          style={{ marginRight: '8px', cursor: onEdit ? 'pointer' : 'default' }}
          onClick={() => onEdit && setIsEditing(true)}
          title={onEdit ? "Click to edit" : ""}
        >
          {formatValue(filter.value)}
        </span>
      )}
      
      <button
        onClick={() => onRemove(filter.field.toString())}
        style={{
          background: 'transparent',
          border: 'none',
          color: 'rgba(255, 255, 255, 0.7)',
          cursor: 'pointer',
          padding: '0',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginLeft: '4px',
        }}
        title="Remove filter"
      >
        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M18 6L6 18M6 6l12 12" />
        </svg>
      </button>
    </div>
  );
};

export default ConfigurationFilterBadge;