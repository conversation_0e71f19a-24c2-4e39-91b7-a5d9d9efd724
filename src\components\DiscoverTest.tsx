import React from 'react';
import { useDiscover } from '../context/DiscoverContext';
import { useDiscoverTime, useDiscoverSearch, useDiscoverFields, useDiscoverLogs } from '../hooks';

/**
 * Test component to verify that the Discover context and hooks are working correctly
 */
const DiscoverTest: React.FC = () => {
  const { state } = useDiscover();
  const { timeRange, formatDate } = useDiscoverTime();
  const { counts, searchQuery, setSearchQuery } = useDiscoverSearch();
  const { selectedFields, availableFields } = useDiscoverFields();
  const { logs } = useDiscoverLogs();
  
  return (
    <div style={{ color: 'white', padding: '20px', background: 'rgba(16, 24, 45, 0.7)', borderRadius: '8px', marginBottom: '20px' }}>
      <h2>Discover Context Test</h2>
      
      <div>
        <h3>Time Range</h3>
        <p>From: {formatDate(timeRange.from)}</p>
        <p>To: {formatDate(timeRange.to)}</p>
        <p>Preset: {timeRange.preset || 'custom'}</p>
      </div>
      
      <div>
        <h3>Search</h3>
        <input 
          type="text" 
          value={searchQuery} 
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="Search logs..."
          style={{ 
            padding: '8px', 
            width: '100%', 
            background: 'rgba(0, 0, 0, 0.3)',
            border: '1px solid rgba(0, 229, 255, 0.3)',
            borderRadius: '4px',
            color: 'white'
          }}
        />
        <p>Total logs: {counts.total}</p>
        <p>Filtered logs: {counts.filtered}</p>
      </div>
      
      <div>
        <h3>Selected Fields</h3>
        <ul>
          {selectedFields.map(field => (
            <li key={field}>{field}</li>
          ))}
        </ul>
      </div>
      
      <div>
        <h3>Available Fields</h3>
        <p>Total fields: {Object.keys(availableFields).length}</p>
      </div>
      
      <div>
        <h3>Logs</h3>
        <p>Showing first 3 logs:</p>
        <ul>
          {logs.slice(0, 3).map(log => (
            <li key={log.id} style={{ marginBottom: '10px' }}>
              <div><strong>Time:</strong> {formatDate(log.timestamp)}</div>
              <div><strong>Source:</strong> {log.source}</div>
              <div><strong>Level:</strong> {log.level}</div>
              <div><strong>Message:</strong> {log.message}</div>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default DiscoverTest;