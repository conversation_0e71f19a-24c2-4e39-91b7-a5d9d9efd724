import React, { useState } from 'react';
import { useDiscoverFields } from '../../hooks';
import FieldSearch from './FieldSearch';
import SelectedFields from './SelectedFields';
import AvailableFields from './AvailableFields';

/**
 * Component for managing field selection in the Discover page
 */
const DiscoverFields: React.FC = () => {
  const { 
    selectedFields, 
    categorizedFields, 
    addField, 
    removeField,
    resetFields
  } = useDiscoverFields();
  
  const [fieldSearchTerm, setFieldSearchTerm] = useState('');
  const [filteredFields, setFilteredFields] = useState<string[]>([]);
  
  // Handle field search
  const handleFieldSearch = (searchTerm: string) => {
    setFieldSearchTerm(searchTerm);
    
    if (!searchTerm) {
      setFilteredFields([]);
      return;
    }
    
    // Filter fields based on search term
    const filtered: string[] = [];
    Object.entries(categorizedFields).forEach(([_, fields]) => {
      Object.keys(fields).forEach(fieldName => {
        if (fieldName.toLowerCase().includes(searchTerm.toLowerCase())) {
          filtered.push(fieldName);
        }
      });
    });
    
    setFilteredFields(filtered);
  };
  
  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      gap: '16px',
    }}>
      {/* Field search component */}
      <FieldSearch 
        onSearch={handleFieldSearch} 
        placeholder="Search fields..."
      />
      
      {/* Selected fields section */}
      <div style={{ marginBottom: '16px' }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '8px',
        }}>
          <h4 style={{ 
            margin: 0, 
            color: 'white',
            fontSize: '14px',
          }}>
            Selected Fields
          </h4>
          
          <button
            onClick={resetFields}
            style={{
              background: 'transparent',
              border: 'none',
              color: '#00e5ff',
              cursor: 'pointer',
              padding: '4px',
              fontSize: '12px',
              textDecoration: 'underline',
            }}
          >
            Reset to default
          </button>
        </div>
        
        <SelectedFields 
          fields={selectedFields} 
          onRemoveField={removeField} 
        />
      </div>
      
      {/* Available fields component */}
      <div>
        <h4 style={{ 
          margin: '0 0 8px 0', 
          color: 'white',
          fontSize: '14px',
        }}>
          Available Fields
        </h4>
        
        <AvailableFields 
          categorizedFields={categorizedFields}
          filteredFields={fieldSearchTerm ? filteredFields : undefined}
          onAddField={addField}
        />
      </div>
    </div>
  );
};

export default DiscoverFields;