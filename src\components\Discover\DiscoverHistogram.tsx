import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useDiscoverTime } from '../../hooks';
import HistogramStats from './HistogramStats';
import HistogramLevelDistribution from './HistogramLevelDistribution';
import HistogramTimeDistribution from './HistogramTimeDistribution';

/**
 * Histogram component for the Discover page
 * Displays log distribution over time with interactive features
 */
const DiscoverHistogram: React.FC = () => {
  const { 
    groupedData, 
    timeInterval, 
    formatDate, 
    setCustomTimeRange,
    timeRange
  } = useDiscoverTime();
  
  // Refs for brush selection
  const containerRef = useRef<HTMLDivElement>(null);
  const brushRef = useRef<HTMLDivElement>(null);
  const chartAreaRef = useRef<HTMLDivElement>(null);
  
  // State for brush selection
  const [brushStart, setBrushStart] = useState<number | null>(null);
  const [brushEnd, setBrushEnd] = useState<number | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [hoveredBar, setHoveredBar] = useState<number | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  
  // Find the maximum count for scaling
  const maxCount = Math.max(...groupedData.map(item => item.count), 1);
  
  // Calculate time range for each bar
  const barTimeRanges = groupedData.map((item, index, array) => {
    let start = item.timestamp;
    let end;
    
    if (index < array.length - 1) {
      end = array[index + 1].timestamp;
    } else {
      // For the last bar, estimate the end time based on the interval
      const msPerBar = index > 0 
        ? array[index].timestamp.getTime() - array[index - 1].timestamp.getTime()
        : 3600000; // Default to 1 hour if we can't calculate
      end = new Date(start.getTime() + msPerBar);
    }
    
    return { start, end };
  });
  
  // Handle mouse down on chart area to start brush selection
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!chartAreaRef.current) return;
    
    const rect = chartAreaRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    
    setBrushStart(x);
    setBrushEnd(x);
    setIsDragging(true);
  }, []);
  
  // Handle mouse move during brush selection
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!isDragging || !chartAreaRef.current) return;
    
    const rect = chartAreaRef.current.getBoundingClientRect();
    const x = Math.max(0, Math.min(e.clientX - rect.left, rect.width));
    
    setBrushEnd(x);
  }, [isDragging]);
  
  // Handle mouse up to complete brush selection
  const handleMouseUp = useCallback(() => {
    if (!isDragging || brushStart === null || brushEnd === null || !chartAreaRef.current) {
      setIsDragging(false);
      return;
    }
    
    const rect = chartAreaRef.current.getBoundingClientRect();
    const totalWidth = rect.width;
    
    // Calculate selected time range based on brush position
    if (Math.abs(brushStart - brushEnd) > 10 && groupedData.length > 0) {
      const startPercent = Math.min(brushStart, brushEnd) / totalWidth;
      const endPercent = Math.max(brushStart, brushEnd) / totalWidth;
      
      const startIndex = Math.floor(startPercent * groupedData.length);
      const endIndex = Math.min(
        Math.floor(endPercent * groupedData.length),
        groupedData.length - 1
      );
      
      const fromDate = barTimeRanges[startIndex].start;
      const toDate = barTimeRanges[endIndex].end;
      
      // Apply the new time range
      setCustomTimeRange(fromDate, toDate);
    }
    
    // Reset brush selection
    setIsDragging(false);
    setBrushStart(null);
    setBrushEnd(null);
  }, [isDragging, brushStart, brushEnd, groupedData, barTimeRanges, setCustomTimeRange]);
  
  // Handle mouse leave to cancel brush selection
  const handleMouseLeave = useCallback(() => {
    if (isDragging) {
      setIsDragging(false);
      setBrushStart(null);
      setBrushEnd(null);
    }
    setHoveredBar(null);
  }, [isDragging]);
  
  // Handle bar hover
  const handleBarHover = useCallback((index: number, e: React.MouseEvent) => {
    setHoveredBar(index);
    setTooltipPosition({ x: e.clientX, y: e.clientY });
  }, []);
  
  // Handle bar click to filter to that time period
  const handleBarClick = useCallback((index: number) => {
    if (index >= 0 && index < barTimeRanges.length) {
      const { start, end } = barTimeRanges[index];
      setCustomTimeRange(start, end);
    }
  }, [barTimeRanges, setCustomTimeRange]);
  
  // Calculate brush position and dimensions
  const brushStyle = {
    display: isDragging && brushStart !== null && brushEnd !== null ? 'block' : 'none',
    left: `${Math.min(brushStart || 0, brushEnd || 0)}px`,
    width: `${Math.abs((brushEnd || 0) - (brushStart || 0))}px`,
  };
  
  // We'll use the HistogramLevelDistribution component for level counts
  
  // Add event listeners for mouse up outside the component
  useEffect(() => {
    const handleGlobalMouseUp = () => {
      if (isDragging) {
        handleMouseUp();
      }
    };
    
    window.addEventListener('mouseup', handleGlobalMouseUp);
    return () => {
      window.removeEventListener('mouseup', handleGlobalMouseUp);
    };
  }, [isDragging, handleMouseUp]);
  
  // Format count with thousands separator
  const formatCount = (count: number): string => {
    return new Intl.NumberFormat().format(count);
  };
  
  // Get total count of logs
  const totalLogs = groupedData.reduce((sum, item) => sum + item.count, 0);
  
  // Get interval label
  const getIntervalLabel = (): string => {
    switch (timeInterval) {
      case 'minute': return 'minute';
      case 'hour': return 'hour';
      case 'day': return 'day';
      case 'week': return 'week';
      default: return 'interval';
    }
  };
  
  return (
    <div 
      ref={containerRef}
      style={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
      }}
    >
      {/* Header with stats */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '12px',
      }}>
        <h3 style={{ 
          margin: 0, 
          color: 'white',
          fontSize: '16px',
        }}>
          Log Distribution Over Time
        </h3>
        
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          fontSize: '12px',
          color: 'rgba(255, 255, 255, 0.7)',
        }}>
          <div>
            <span style={{ fontWeight: 'bold' }}>{formatCount(totalLogs)}</span> logs
          </div>
          <div>
            <span style={{ fontWeight: 'bold' }}>{groupedData.length}</span> {getIntervalLabel()}s
          </div>
          <div style={{ fontSize: '11px', color: 'rgba(255, 255, 255, 0.5)' }}>
            Drag to zoom
          </div>
        </div>
      </div>
      
      {/* Time-based histogram visualization */}
      <HistogramTimeDistribution height={200} />
      
      {/* Level distribution compact view (optional, remove if not needed elsewhere) */}
    </div>
  );
};

export default DiscoverHistogram;