import React, { useState, useRef, useEffect, useCallback, useMemo, KeyboardEvent } from 'react';
import { useDiscoverLogs, useDiscoverFields } from '../../hooks';
import LogEntryRow from './LogEntryRow';
import LogEntryDetails from './LogEntryDetails';
import TableColumnResizer from './TableColumnResizer';
import PaginationControls from './PaginationControls';
import PageSizeSelector from './PageSizeSelector';

/**
 * Table component for the Discover page
 * Displays log entries with expandable details and virtual scrolling
 */
const DiscoverTable: React.FC = () => {
  const { logs, allLogs, selectedFields, toggleLogExpansion, isLogExpanded, getFieldValue, pagination } = useDiscoverLogs();
  const { toggleField } = useDiscoverFields();
  
  // Refs for virtual scrolling
  const tableRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);
  
  // State for virtual scrolling and column management
  const [visibleStartIndex, setVisibleStartIndex] = useState(0);
  const [visibleEndIndex, setVisibleEndIndex] = useState(50);
  const [scrollTop, setScrollTop] = useState(0);
  const [columnWidths, setColumnWidths] = useState<Record<string, number>>({});
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  
  // Constants for virtual scrolling
  const rowHeight = 40; // Approximate height of each row
  const bufferSize = 10; // Number of extra rows to render above and below visible area
  
  // Calculate visible rows based on scroll position
  const calculateVisibleRows = useCallback(() => {
    if (!tableRef.current) return;
    
    const containerHeight = tableRef.current.clientHeight;
    const scrollPosition = tableRef.current.scrollTop;
    
    const startIndex = Math.max(0, Math.floor(scrollPosition / rowHeight) - bufferSize);
    const endIndex = Math.min(
      logs.length - 1,
      Math.ceil((scrollPosition + containerHeight) / rowHeight) + bufferSize
    );
    
    setVisibleStartIndex(startIndex);
    setVisibleEndIndex(endIndex);
    setScrollTop(scrollPosition);
  }, [logs.length, rowHeight, bufferSize]);
  
  // Handle scroll event
  const handleScroll = useCallback(() => {
    requestAnimationFrame(calculateVisibleRows);
  }, [calculateVisibleRows]);
  
  // Initialize column widths
  const initializeColumnWidths = useCallback(() => {
    if (!headerRef.current) return;
    
    const headerCells = headerRef.current.querySelectorAll('[data-field]');
    const newWidths: Record<string, number> = {};
    
    headerCells.forEach((cell) => {
      const field = cell.getAttribute('data-field');
      if (field) {
        newWidths[field] = cell.clientWidth;
      }
    });
    
    setColumnWidths(newWidths);
  }, []);
  
  // Initialize virtual scrolling
  useEffect(() => {
    calculateVisibleRows();
    initializeColumnWidths();
    
    window.addEventListener('resize', initializeColumnWidths);
    return () => {
      window.removeEventListener('resize', initializeColumnWidths);
    };
  }, [calculateVisibleRows, initializeColumnWidths]);
  
  // Update visible rows when logs change
  useEffect(() => {
    calculateVisibleRows();
  }, [logs, calculateVisibleRows]);
  
  // Sort logs if needed
  const sortedLogs = useMemo(() => {
    if (!sortField) return logs;
    
    return [...logs].sort((a, b) => {
      const aValue = getFieldValue(a, sortField);
      const bValue = getFieldValue(b, sortField);
      
      // Handle undefined values
      if (aValue === undefined && bValue === undefined) return 0;
      if (aValue === undefined) return sortDirection === 'asc' ? -1 : 1;
      if (bValue === undefined) return sortDirection === 'asc' ? 1 : -1;
      
      // Compare values based on type
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc' 
          ? aValue.localeCompare(bValue) 
          : bValue.localeCompare(aValue);
      }
      
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'asc' 
          ? aValue - bValue 
          : bValue - aValue;
      }
      
      if (aValue instanceof Date && bValue instanceof Date) {
        return sortDirection === 'asc' 
          ? aValue.getTime() - bValue.getTime() 
          : bValue.getTime() - aValue.getTime();
      }
      
      // Default string comparison
      const aStr = String(aValue);
      const bStr = String(bValue);
      return sortDirection === 'asc' 
        ? aStr.localeCompare(bStr) 
        : bStr.localeCompare(aStr);
    });
  }, [logs, sortField, sortDirection]);
  
  // Get visible logs
  const visibleLogs = sortedLogs.slice(visibleStartIndex, visibleEndIndex + 1);
  
  // Calculate total height of all rows
  const totalHeight = logs.length * rowHeight;
  
  // Calculate padding to maintain scroll position
  const topPadding = visibleStartIndex * rowHeight;
  const bottomPadding = Math.max(0, totalHeight - topPadding - visibleLogs.length * rowHeight);
  
  // Handle column click for sorting
  const handleColumnClick = (field: string) => {
    if (sortField === field) {
      // Toggle sort direction if clicking the same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new sort field and default to descending
      setSortField(field);
      setSortDirection('desc');
    }
  };
  
  // Handle column resize
  const handleColumnResize = (field: string, width: number) => {
    setColumnWidths(prev => ({
      ...prev,
      [field]: width
    }));
  };
  
  return (
    <div style={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
    }}>
      {/* Scrollable table area (header + body) */}
      <div style={{
        flex: 1,
        minHeight: 0,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
      }}>
        {/* Table header */}
        <div 
          ref={headerRef}
          role="row"
          aria-rowindex={1}
          style={{
            display: 'flex',
            borderBottom: '1px solid rgba(0, 229, 255, 0.2)',
            background: 'rgba(10, 14, 23, 0.8)',
            padding: '8px 16px',
            color: 'white',
            fontWeight: 'bold',
            fontSize: '14px',
            position: 'sticky',
            top: 0,
            zIndex: 10,
          }}
        >
          <div style={{ width: '30px' }} role="columnheader" aria-hidden="true"></div>
          {selectedFields.map((field, index) => (
            <div 
              key={field}
              data-field={field}
              role="columnheader"
              aria-sort={sortField === field ? (sortDirection === 'asc' ? 'ascending' : 'descending') : 'none'}
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  handleColumnClick(field);
                }
              }}
              style={{ 
                flexGrow: field === 'message' ? 3 : 1,
                padding: '0 8px',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                cursor: 'pointer',
                userSelect: 'none',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                position: 'relative',
                width: columnWidths[field] ? `${columnWidths[field]}px` : undefined,
                minWidth: columnWidths[field] ? `${columnWidths[field]}px` : undefined,
                maxWidth: columnWidths[field] ? `${columnWidths[field]}px` : undefined,
              }}
              onClick={() => handleColumnClick(field)}
            >
              <span>{field}</span>
              <span 
                aria-hidden="true"
                style={{ 
                  color: 'rgba(255, 255, 255, 0.5)', 
                  fontSize: '12px', 
                  marginLeft: '4px',
                  transform: sortField === field && sortDirection === 'asc' ? 'rotate(180deg)' : 'none',
                  opacity: sortField === field ? 1 : 0.5,
                }}
              >
                ↓
              </span>
              <TableColumnResizer 
                index={index} 
                onResize={(_, width) => handleColumnResize(field, width)} 
              />
            </div>
          ))}
        </div>
        {/* Table body with virtual scrolling */}
        <div 
          ref={tableRef}
          style={{
            flex: 1,
            minHeight: 0,
            overflowY: 'auto',
            position: 'relative',
          }}
          onScroll={handleScroll}
        >
          {logs.length === 0 ? (
            <div style={{
              padding: '24px',
              textAlign: 'center',
              color: 'rgba(255, 255, 255, 0.7)',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '200px',
              gap: '16px',
            }}>
              <div style={{
                fontSize: '18px',
                fontWeight: 'bold',
              }}>
                No logs found
              </div>
              <div style={{
                fontSize: '14px',
                maxWidth: '500px',
                lineHeight: '1.5',
              }}>
                No logs match your current filters. Try adjusting your search criteria or time range.
              </div>
            </div>
          ) : (
            <div style={{ height: totalHeight, position: 'relative' }}>
              <div style={{ paddingTop: topPadding, paddingBottom: bottomPadding }}>
                {visibleLogs.map(log => (
                  <React.Fragment key={log.id}>
                    {/* Log row */}
                    <LogEntryRow 
                      log={log}
                      isExpanded={isLogExpanded(log.id)}
                      onToggleExpand={() => toggleLogExpansion(log.id)}
                      selectedFields={selectedFields}
                      columnWidths={columnWidths}
                    />
                    {/* Expanded log details */}
                    {isLogExpanded(log.id) && (
                      <LogEntryDetails 
                        log={log} 
                        onClose={() => toggleLogExpansion(log.id)}
                      />
                    )}
                  </React.Fragment>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
      {/* Pagination controls (always visible at bottom) */}
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '8px 0',
        background: 'rgba(10, 14, 23, 0.8)',
        borderTop: '1px solid rgba(0, 229, 255, 0.2)',
      }}>
        <PaginationControls
          currentPage={pagination.currentPage}
          totalPages={pagination.totalPages}
          onPageChange={pagination.setCurrentPage}
          onFirstPage={pagination.goToFirstPage}
          onPreviousPage={pagination.goToPreviousPage}
          onNextPage={pagination.goToNextPage}
          onLastPage={pagination.goToLastPage}
          isFirstPage={pagination.isFirstPage}
          isLastPage={pagination.isLastPage}
          isLoading={pagination.isLoading}
          noResults={pagination.noResults}
        />
      </div>
      {/* Status bar (always visible at very bottom) */}
      <div style={{
        padding: '8px 16px',
        borderTop: '1px solid rgba(0, 229, 255, 0.2)',
        background: 'rgba(10, 14, 23, 0.8)',
        color: 'rgba(255, 255, 255, 0.7)',
        fontSize: '12px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}>
        <div>
          {pagination.noResults ? (
            <span style={{ color: 'rgba(255, 255, 255, 0.5)', fontStyle: 'italic' }}>
              No logs match your current filters
            </span>
          ) : (
            `${allLogs.length} logs found`
          )}
        </div>
        <PageSizeSelector
          pageSize={pagination.pageSize}
          onPageSizeChange={pagination.setPageSize}
          options={[10, 25, 50, 100]}
          noResults={pagination.noResults}
        />
        <div>
          {pagination.showingText}
        </div>
      </div>
    </div>
  );
};

export default DiscoverTable;