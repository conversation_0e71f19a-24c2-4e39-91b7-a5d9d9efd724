import React, { useState } from 'react';
import { useDiscoverFields } from '../../hooks';
import FieldDetails from './FieldDetails';
import { useDiscover, discoverActions } from '../../context/DiscoverContext';

interface FieldInfo {
  type: string;
  count: number;
  topValues?: Array<{value: any, count: number}>;
}

interface FieldCategoryProps {
  category: string;
  fields: {
    [fieldName: string]: FieldInfo;
  };
  isExpanded: boolean;
  onToggleExpand: () => void;
  onAddField: (field: string) => void;
}

/**
 * Component for displaying a category of fields in the Discover sidebar
 */
const FieldCategory: React.FC<FieldCategoryProps> = ({ 
  category, 
  fields, 
  isExpanded, 
  onToggleExpand, 
  onAddField 
}) => {
  const { formatFieldValue } = useDiscoverFields();
  const { dispatch } = useDiscover();
  const fieldNames = Object.keys(fields);
  
  const [selectedField, setSelectedField] = useState<string | null>(null);
  
  // Handle field click to show details
  const handleFieldClick = (fieldName: string) => {
    setSelectedField(fieldName);
  };
  
  // Handle adding a filter
  const handleAddFilter = (field: string, value: any, operator: string) => {
    dispatch(discoverActions.addFilter({
      field,
      operator: operator as any,
      value,
      enabled: true
    }));
    setSelectedField(null);
  };
  
  return (
    <div style={{ marginBottom: '16px' }}>
      <button
        onClick={onToggleExpand}
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          width: '100%',
          padding: '8px',
          background: 'rgba(0, 0, 0, 0.3)',
          border: 'none',
          borderRadius: '4px',
          color: 'rgba(255, 255, 255, 0.9)',
          cursor: 'pointer',
          textAlign: 'left',
          fontSize: '13px',
          fontWeight: 'bold',
          marginBottom: '4px',
        }}
      >
        <span>{category} ({fieldNames.length})</span>
        <svg 
          width="16" 
          height="16" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="currentColor" 
          strokeWidth="2"
          style={{
            transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
            transition: 'transform 0.2s ease',
          }}
        >
          <path d="M6 9l6 6 6-6" />
        </svg>
      </button>
      
      {isExpanded && (
        <ul style={{ 
          listStyle: 'none', 
          padding: 0, 
          margin: 0,
          marginLeft: '8px',
        }}>
          {fieldNames.map(fieldName => (
            <li 
              key={fieldName}
              style={{
                padding: '6px 8px',
                marginBottom: '2px',
                background: 'rgba(0, 0, 0, 0.2)',
                borderRadius: '4px',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                color: 'white',
                fontSize: '13px',
                cursor: 'pointer',
              }}
              onClick={() => handleFieldClick(fieldName)}
            >
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                overflow: 'hidden',
              }}>
                <span style={{ 
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}>
                  {fieldName}
                </span>
                <span style={{ 
                  fontSize: '11px', 
                  color: 'rgba(255, 255, 255, 0.5)',
                  marginTop: '2px',
                }}>
                  {fields[fieldName].type} • {fields[fieldName].count} occurrences
                </span>
              </div>
              <button
                onClick={(e) => {
                  e.stopPropagation(); // Prevent opening field details
                  onAddField(fieldName);
                }}
                aria-label={`Add ${fieldName} field`}
                style={{
                  background: 'transparent',
                  border: 'none',
                  color: '#00e5ff',
                  cursor: 'pointer',
                  padding: '2px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexShrink: 0,
                }}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M12 5v14M5 12h14" />
                </svg>
              </button>
            </li>
          ))}
        </ul>
      )}
      
      {/* Field details modal */}
      {selectedField && (
        <FieldDetails
          fieldName={selectedField}
          fieldType={fields[selectedField].type}
          totalCount={fields[selectedField].count}
          topValues={fields[selectedField].topValues}
          onAddFilter={handleAddFilter}
          onClose={() => setSelectedField(null)}
        />
      )}
    </div>
  );
};

export default FieldCategory;