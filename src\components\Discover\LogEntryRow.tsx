import React from 'react';
import { LogEntry } from '../../types/discover';
import { useDiscoverLogs } from '../../hooks';

interface LogEntryRowProps {
  log: LogEntry;
  isExpanded: boolean;
  onToggleExpand: () => void;
  selectedFields: string[];
  columnWidths?: Record<string, number>;
}

/**
 * Component for displaying a single log entry row in the table
 */
const LogEntryRow: React.FC<LogEntryRowProps> = ({
  log,
  isExpanded,
  onToggleExpand,
  selectedFields,
  columnWidths = {}
}) => {
  const { getFieldValue, formatFieldValue, getLogLevelColor } = useDiscoverLogs();

  // Helper function to get flex grow for different fields
  const getFieldFlexGrow = (field: string): number => {
    switch (field) {
      case 'timestamp': return 1.2;
      case 'source': return 1;
      case 'message': return 3;
      case 'level': return 0.8;
      case 'rule.description': return 1.5;
      default: return 1;
    }
  };

  // Helper function to get minimum width for different fields
  const getFieldMinWidth = (field: string): string => {
    switch (field) {
      case 'timestamp': return '140px';
      case 'source': return '100px';
      case 'message': return '200px';
      case 'level': return '80px';
      case 'rule.description': return '120px';
      default: return '100px';
    }
  };
  
  return (
    <div 
      role="row"
      aria-expanded={isExpanded}
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onToggleExpand();
        }
      }}
      style={{
        display: 'flex',
        borderBottom: '1px solid rgba(0, 229, 255, 0.1)',
        padding: '4px 16px',
        color: 'white',
        fontSize: '13px',
        cursor: 'pointer',
        background: isExpanded
          ? 'rgba(0, 229, 255, 0.05)'
          : 'transparent',
        transition: 'background-color 0.2s',
        alignItems: 'center',
        outline: 'none',
        minHeight: '32px',
      }}
      onClick={onToggleExpand}
    >
      <div style={{ width: '24px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <svg
          width="12"
          height="12"
          viewBox="0 0 24 24"
          fill="none"
          stroke="#00e5ff"
          strokeWidth="2"
          style={{
            transform: isExpanded ? 'rotate(90deg)' : 'rotate(0deg)',
            transition: 'transform 0.2s',
          }}
        >
          <polyline points="9 18 15 12 9 6" />
        </svg>
      </div>
      
      {selectedFields.map(field => {
        const value = getFieldValue(log, field);
        
        // Special styling for level field
        if (field === 'level') {
          const levelColor = getLogLevelColor(value as string);
          return (
            <div
              key={field}
              style={{
                flexGrow: getFieldFlexGrow(field),
                flexShrink: 1,
                padding: '0 6px',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                color: levelColor,
                fontWeight: 'bold',
                minWidth: getFieldMinWidth(field),
              }}
            >
              {formatFieldValue(value)}
            </div>
          );
        }
        
        return (
          <div
            key={field}
            style={{
              flexGrow: columnWidths[field] ? 0 : getFieldFlexGrow(field),
              flexShrink: 1,
              padding: '0 6px',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              width: columnWidths[field] ? `${columnWidths[field]}px` : undefined,
              minWidth: columnWidths[field] ? `${columnWidths[field]}px` : getFieldMinWidth(field),
              maxWidth: columnWidths[field] ? `${columnWidths[field]}px` : undefined,
            }}
          >
            {formatFieldValue(value)}
          </div>
        );
      })}
    </div>
  );
};

export default LogEntryRow;