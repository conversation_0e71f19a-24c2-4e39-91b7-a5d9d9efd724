import React from 'react';
import './PaginationControls.css';

/**
 * Props for the PaginationControls component
 */
interface PaginationControlsProps {
  /**
   * Current page number (1-based)
   */
  currentPage: number;
  
  /**
   * Total number of pages
   */
  totalPages: number;
  
  /**
   * Function to navigate to a specific page
   */
  onPageChange: (page: number) => void;
  
  /**
   * Function to navigate to the first page
   */
  onFirstPage: () => void;
  
  /**
   * Function to navigate to the previous page
   */
  onPreviousPage: () => void;
  
  /**
   * Function to navigate to the next page
   */
  onNextPage: () => void;
  
  /**
   * Function to navigate to the last page
   */
  onLastPage: () => void;
  
  /**
   * Whether the current page is the first page
   */
  isFirstPage: boolean;
  
  /**
   * Whether the current page is the last page
   */
  isLastPage: boolean;
  
  /**
   * Whether the data is currently loading
   */
  isLoading?: boolean;
  
  /**
   * Whether there are no results to paginate
   */
  noResults?: boolean;
}

/**
 * Pagination controls component for navigating through pages of data
 */
const PaginationControls: React.FC<PaginationControlsProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  onFirstPage,
  onPreviousPage,
  onNextPage,
  onLastPage,
  isFirstPage,
  isLastPage,
  isLoading = false,
  noResults = false,
}) => {
  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent, action: () => void) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      action();
    }
  };

  // If there are no results, show a message instead of pagination controls
  if (noResults) {
    return (
      <div 
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '8px',
          padding: '8px 0',
          backgroundColor: 'rgba(10, 14, 23, 0.6)',
          borderTop: '1px solid rgba(0, 229, 255, 0.2)',
          borderBottom: '1px solid rgba(0, 229, 255, 0.2)',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
          color: 'rgba(255, 255, 255, 0.5)',
          fontStyle: 'italic',
        }}
      >
        No results to paginate
      </div>
    );
  }

  return (
    <div 
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '8px',
        padding: '8px 0',
        backgroundColor: 'rgba(10, 14, 23, 0.6)',
        borderTop: '1px solid rgba(0, 229, 255, 0.2)',
        borderBottom: '1px solid rgba(0, 229, 255, 0.2)',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
      }}
      role="navigation"
      aria-label="Pagination"
    >
      {/* Loading indicator */}
      {isLoading && (
        <div
          className="loading-indicator"
          role="progressbar"
          aria-label="Loading"
        />
      )}
      
      {/* First page button */}
      <button
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '32px',
          height: '32px',
          borderRadius: '4px',
          border: '1px solid rgba(0, 229, 255, 0.3)',
          background: 'rgba(10, 14, 23, 0.8)',
          color: isFirstPage || isLoading ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 229, 255, 0.8)',
          cursor: isFirstPage || isLoading ? 'not-allowed' : 'pointer',
          transition: 'all 0.2s ease',
          opacity: isLoading ? 0.7 : 1,
          ':hover': {
            backgroundColor: isFirstPage || isLoading ? 'rgba(10, 14, 23, 0.8)' : 'rgba(0, 229, 255, 0.1)',
            borderColor: isFirstPage || isLoading ? 'rgba(0, 229, 255, 0.3)' : 'rgba(0, 229, 255, 0.5)',
          },
        }}
        onClick={isFirstPage || isLoading ? undefined : onFirstPage}
        onKeyDown={(e) => !isFirstPage && !isLoading && handleKeyDown(e, onFirstPage)}
        disabled={isFirstPage || isLoading}
        aria-label="First page"
        aria-disabled={isFirstPage || isLoading}
        title="First page"
        onMouseOver={(e) => {
          if (!isFirstPage && !isLoading) {
            e.currentTarget.style.backgroundColor = 'rgba(0, 229, 255, 0.1)';
            e.currentTarget.style.borderColor = 'rgba(0, 229, 255, 0.5)';
          }
        }}
        onMouseOut={(e) => {
          e.currentTarget.style.backgroundColor = 'rgba(10, 14, 23, 0.8)';
          e.currentTarget.style.borderColor = 'rgba(0, 229, 255, 0.3)';
        }}
      >
        ⟪
      </button>

      {/* Previous page button */}
      <button
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '32px',
          height: '32px',
          borderRadius: '4px',
          border: '1px solid rgba(0, 229, 255, 0.3)',
          background: 'rgba(10, 14, 23, 0.8)',
          color: isFirstPage || isLoading ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 229, 255, 0.8)',
          cursor: isFirstPage || isLoading ? 'not-allowed' : 'pointer',
          transition: 'all 0.2s ease',
          opacity: isLoading ? 0.7 : 1,
        }}
        onClick={isFirstPage || isLoading ? undefined : onPreviousPage}
        onKeyDown={(e) => !isFirstPage && !isLoading && handleKeyDown(e, onPreviousPage)}
        disabled={isFirstPage || isLoading}
        aria-label="Previous page"
        aria-disabled={isFirstPage || isLoading}
        title="Previous page"
        onMouseOver={(e) => {
          if (!isFirstPage && !isLoading) {
            e.currentTarget.style.backgroundColor = 'rgba(0, 229, 255, 0.1)';
            e.currentTarget.style.borderColor = 'rgba(0, 229, 255, 0.5)';
          }
        }}
        onMouseOut={(e) => {
          e.currentTarget.style.backgroundColor = 'rgba(10, 14, 23, 0.8)';
          e.currentTarget.style.borderColor = 'rgba(0, 229, 255, 0.3)';
        }}
      >
        ⟨
      </button>

      {/* Page indicator */}
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          minWidth: '120px',
          height: '32px',
          padding: '0 12px',
          borderRadius: '4px',
          border: '1px solid rgba(0, 229, 255, 0.3)',
          background: 'rgba(10, 14, 23, 0.8)',
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: '14px',
          fontFamily: 'monospace',
          userSelect: 'none',
        }}
        aria-live="polite"
        aria-atomic="true"
      >
        <span style={{ color: 'rgba(0, 229, 255, 0.8)' }}>Page</span>
        <span style={{ margin: '0 4px', fontWeight: 'bold' }}>{currentPage}</span>
        <span style={{ color: 'rgba(255, 255, 255, 0.5)' }}>of</span>
        <span style={{ margin: '0 4px', fontWeight: 'bold' }}>{totalPages}</span>
      </div>

      {/* Next page button */}
      <button
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '32px',
          height: '32px',
          borderRadius: '4px',
          border: '1px solid rgba(0, 229, 255, 0.3)',
          background: 'rgba(10, 14, 23, 0.8)',
          color: isLastPage || isLoading ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 229, 255, 0.8)',
          cursor: isLastPage || isLoading ? 'not-allowed' : 'pointer',
          transition: 'all 0.2s ease',
          opacity: isLoading ? 0.7 : 1,
        }}
        onClick={isLastPage || isLoading ? undefined : onNextPage}
        onKeyDown={(e) => !isLastPage && !isLoading && handleKeyDown(e, onNextPage)}
        disabled={isLastPage || isLoading}
        aria-label="Next page"
        aria-disabled={isLastPage || isLoading}
        title="Next page"
        onMouseOver={(e) => {
          if (!isLastPage && !isLoading) {
            e.currentTarget.style.backgroundColor = 'rgba(0, 229, 255, 0.1)';
            e.currentTarget.style.borderColor = 'rgba(0, 229, 255, 0.5)';
          }
        }}
        onMouseOut={(e) => {
          e.currentTarget.style.backgroundColor = 'rgba(10, 14, 23, 0.8)';
          e.currentTarget.style.borderColor = 'rgba(0, 229, 255, 0.3)';
        }}
      >
        ⟩
      </button>

      {/* Last page button */}
      <button
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '32px',
          height: '32px',
          borderRadius: '4px',
          border: '1px solid rgba(0, 229, 255, 0.3)',
          background: 'rgba(10, 14, 23, 0.8)',
          color: isLastPage || isLoading ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 229, 255, 0.8)',
          cursor: isLastPage || isLoading ? 'not-allowed' : 'pointer',
          transition: 'all 0.2s ease',
          opacity: isLoading ? 0.7 : 1,
        }}
        onClick={isLastPage || isLoading ? undefined : onLastPage}
        onKeyDown={(e) => !isLastPage && !isLoading && handleKeyDown(e, onLastPage)}
        disabled={isLastPage || isLoading}
        aria-label="Last page"
        aria-disabled={isLastPage || isLoading}
        title="Last page"
        onMouseOver={(e) => {
          if (!isLastPage && !isLoading) {
            e.currentTarget.style.backgroundColor = 'rgba(0, 229, 255, 0.1)';
            e.currentTarget.style.borderColor = 'rgba(0, 229, 255, 0.5)';
          }
        }}
        onMouseOut={(e) => {
          e.currentTarget.style.backgroundColor = 'rgba(10, 14, 23, 0.8)';
          e.currentTarget.style.borderColor = 'rgba(0, 229, 255, 0.3)';
        }}
      >
        ⟫
      </button>
    </div>
  );
};

export default PaginationControls;