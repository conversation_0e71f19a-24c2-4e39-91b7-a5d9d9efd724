import React from 'react';
import { ExploreNavItemProps } from './types';
import { ExploreIcon, ChevronDownIcon } from './icons';

const ExploreNavItem: React.FC<ExploreNavItemProps & { isActive?: boolean }> = ({
  isCollapsed,
  isExpanded,
  onToggle,
  activeSubItem,
  isActive = false
}) => {
  const getNavItemStyle = () => {
    // Use isActive prop directly
    return {
      width: isCollapsed ? '36px' : '80%',
      height: isCollapsed ? '36px' : '40px',
      display: 'flex',
      justifyContent: isCollapsed ? 'center' : 'space-between',
      alignItems: 'center',
      borderRadius: '8px',
      padding: isCollapsed ? '0' : '0 12px',
      cursor: 'pointer',
      color: isActive ? '#00e5ff' : 'white',
      backgroundColor: isActive ? 'rgba(0, 229, 255, 0.15)' : 'rgba(255, 255, 255, 0.03)',
      border: isActive ? '1px solid rgba(0, 229, 255, 0.5)' : '1px solid rgba(255, 255, 255, 0.03)',
      boxShadow: isActive ? '0 0 15px rgba(0, 229, 255, 0.5)' : 'none',
      transition: 'all 0.3s ease',
      marginBottom: '15px',
      backdropFilter: 'blur(5px)',
      WebkitBackdropFilter: 'blur(5px)',
      position: 'relative' as const,
      zIndex: 5,
    };
  };

  const getChevronStyle = () => ({
    transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
    transition: 'transform 0.3s ease',
    opacity: isCollapsed ? 0 : 1,
  });

  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    e.preventDefault();
    onToggle();
  };

  return (
    <div 
      onClick={handleClick}
      style={getNavItemStyle()}
      role="button"
      aria-expanded={isExpanded}
      aria-label="Explore navigation menu"
      aria-haspopup="menu"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onToggle();
        }
        if (e.key === 'Escape' && isExpanded) {
          onToggle();
        }
      }}
    >
      <div style={{ 
        display: 'flex', 
        alignItems: 'center',
        gap: isCollapsed ? '0' : '10px'
      }}>
        <ExploreIcon />
        {!isCollapsed && (
          <span style={{ 
            fontSize: '14px',
            display: 'inline-block',
            transition: 'opacity 0.2s ease',
            opacity: isCollapsed ? 0 : 1
          }}>
            Explore
          </span>
        )}
      </div>
      {!isCollapsed && (
        <div style={getChevronStyle()}>
          <ChevronDownIcon />
        </div>
      )}
    </div>
  );
};

export default ExploreNavItem;