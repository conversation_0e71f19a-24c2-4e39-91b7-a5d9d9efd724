import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ROUTES } from '../../router/constants';
import { ExploreNavItem, ExploreDropdown, EndpointSecurityNavItem, EndpointSecurityDropdown } from '../ExploreNavigation';
import '../ExploreNavigation/ExploreNavigation.css';
import { useNavigation } from '../../context/NavigationContext';

interface NavigationSidebarProps {
  collapsed?: boolean;
  toggleSidebar?: () => void;
  currentPath?: string;
}

/**
 * Navigation sidebar component.
 * 
 * This component provides the main navigation for the application,
 * including links to all main sections and the explore sub-sections.
 */
const NavigationSidebar: React.FC<NavigationSidebarProps> = ({ 
  collapsed: propCollapsed, 
  toggleSidebar: propToggleSidebar,
  currentPath
}) => {
  // Use navigation context if available, otherwise use props
  const navigation = useNavigation();
  const [isHovering, setIsHovering] = useState(false);
  const [animationPhase, setAnimationPhase] = useState(0);
  
  // Use context values if available, otherwise use local state
  const collapsed = navigation ? navigation.state.collapsed : propCollapsed;
  const toggleSidebar = navigation ? navigation.toggleSidebar : propToggleSidebar;
  const activeNav = navigation ? navigation.state.activeNav : 'dashboard';
  const exploreExpanded = navigation ? navigation.state.exploreExpanded : false;
  const activeExploreItem = navigation ? navigation.state.activeExploreItem : undefined;
  const endpointSecurityExpanded = navigation ? navigation.state.endpointSecurityExpanded : false;
  const activeEndpointSecurityItem = navigation ? navigation.state.activeEndpointSecurityItem : undefined;
  
  // Animation effect for the sidebar background
  useEffect(() => {
    const interval = setInterval(() => {
      setAnimationPhase(prev => (prev + 1) % 100);
    }, 150);
    return () => clearInterval(interval);
  }, []);
  
  // Add a constant to use for icon properties
  const iconProps = {
    style: { pointerEvents: "none" as const }
  };
  
  // SVG icons for navigation
  const ShieldIcon = () => (
    <svg
      {...iconProps}
      viewBox="0 0 24 24"
      width="22"
      height="22"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" />
    </svg>
  );

  const HomeIcon = () => (
    <svg
      {...iconProps}
      viewBox="0 0 24 24"
      width="22"
      height="22"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
      <polyline points="9 22 9 12 15 12 15 22" />
    </svg>
  );

  const GridIcon = () => (
    <svg
      {...iconProps}
      viewBox="0 0 24 24"
      width="22"
      height="22"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <rect x="3" y="3" width="7" height="7" />
      <rect x="14" y="3" width="7" height="7" />
      <rect x="14" y="14" width="7" height="7" />
      <rect x="3" y="14" width="7" height="7" />
    </svg>
  );

  const ClockIcon = () => (
    <svg
      {...iconProps}
      viewBox="0 0 24 24"
      width="22"
      height="22"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <circle cx="12" cy="12" r="10" />
      <polyline points="12 6 12 12 16 14" />
    </svg>
  );

  const FileIcon = () => (
    <svg
      {...iconProps}
      viewBox="0 0 24 24"
      width="22"
      height="22"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
      <polyline points="14 2 14 8 20 8" />
    </svg>
  );

  const UserIcon = () => (
    <svg
      {...iconProps}
      viewBox="0 0 24 24"
      width="22"
      height="22"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
      <circle cx="12" cy="7" r="4" />
    </svg>
  );

  const SettingsIcon = () => (
    <svg
      {...iconProps}
      viewBox="0 0 24 24"
      width="22"
      height="22"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <circle cx="12" cy="12" r="3" />
      <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" />
    </svg>
  );
  
  // Navigation item styling function
  const getNavItemStyle = (itemId: string) => {
    return {
      width: collapsed ? '36px' : '80%',
      height: collapsed ? '36px' : '40px',
      display: 'flex',
      justifyContent: collapsed ? 'center' : 'flex-start',
      alignItems: 'center',
      borderRadius: '8px',
      padding: collapsed ? '0' : '0 12px',
      cursor: 'pointer',
      color: activeNav === itemId ? '#00e5ff' : 'white',
      backgroundColor: activeNav === itemId ? 'rgba(0, 229, 255, 0.15)' : 'rgba(255, 255, 255, 0.03)',
      border: activeNav === itemId ? '1px solid rgba(0, 229, 255, 0.5)' : '1px solid rgba(255, 255, 255, 0.03)',
      boxShadow: activeNav === itemId ? '0 0 15px rgba(0, 229, 255, 0.5)' : 'none',
      transition: 'all 0.3s ease',
      marginBottom: '15px',
      backdropFilter: 'blur(5px)',
      WebkitBackdropFilter: 'blur(5px)',
      position: 'relative' as const,
      zIndex: 5,
    };
  };
  
  // Handle navigation click
  const handleNavClick = (navId: string, path: string) => {
    if (navigation) {
      navigation.setActiveNav(navId);
    } else {
      // Fallback to direct navigation if context is not available
      // This case should ideally not happen if navigation context is always present
      // For now, we'll just log a warning or remove if not needed.
      console.warn(`Navigation context not available, cannot navigate to ${path}`);
    }
  };
  
  // Handle explore navigation toggle
  const handleExploreToggle = () => {
    if (navigation) {
      navigation.toggleExplore();
    } else {
      // Fallback if context is not available
      // This case should ideally not happen
      console.warn('Navigation context not available, cannot toggle explore');
    }
  };
  
  // Handle explore sub-item click
  const handleExploreItemClick = (itemId: string) => {
    if (navigation) {
      navigation.setActiveExploreItem(itemId);
    } else {
      // Fallback to direct navigation if context is not available
      // This case should ideally not happen
      console.warn('Navigation context not available, cannot set active explore item');
    }
  };

  const handleEndpointSecurityToggle = () => {
    if (navigation) {
      navigation.toggleEndpointSecurity();
    } else {
      // Fallback if context is not available
      console.warn('Navigation context not available, cannot toggle endpoint security');
    }
  };
  const handleEndpointSecurityItemClick = (itemId: string) => {
    if (navigation) {
      navigation.setActiveEndpointSecurityItem(itemId);
    } else {
      // Fallback if context is not available
      console.warn('Navigation context not available, cannot set active endpoint security item');
    }
  };
  
  return (
    <>
      {/* Navigation Sidebar */}
      <div style={{
        position: 'relative',
        width: collapsed ? '80px' : '200px',
        background: `linear-gradient(135deg, 
                    rgba(16, 24, 45, 0.95) 0%, 
                    rgba(25, 35, 60, 0.95) 50%,
                    rgba(16, 24, 45, 0.95) 100%)`,
        borderRight: '1px solid rgba(0, 229, 255, 0.1)',
        padding: '16px 0',
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        boxSizing: 'border-box',
        transition: 'all 0.3s ease',
        boxShadow: '0 0 25px rgba(0, 229, 255, 0.15), 0 0 50px rgba(0, 229, 255, 0.05)',
        backdropFilter: 'blur(10px)',
        WebkitBackdropFilter: 'blur(10px)',
        overflow: 'visible',
      }}>
        {/* Animated security grid background effect */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          pointerEvents: 'none',
          backgroundImage: `
            radial-gradient(circle at ${50 + Math.sin(animationPhase * 0.05) * 10}% ${50 + Math.cos(animationPhase * 0.05) * 10}%, 
              rgba(0, 229, 255, 0.15) 0%, 
              transparent 50%),
            linear-gradient(${animationPhase * 3.6}deg, rgba(0, 229, 255, 0.03) 25%, transparent 25%, transparent 50%, 
              rgba(0, 229, 255, 0.03) 50%, rgba(0, 229, 255, 0.03) 75%, transparent 75%, transparent)
          `,
          backgroundSize: '100% 100%, 8px 8px',
          opacity: 0.4,
          zIndex: 0,
        }} />

        {/* Hexagon pattern overlay */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          pointerEvents: 'none',
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M30 5 L55 20 L55 50 L30 65 L5 50 L5 20 Z' stroke='rgba(0, 229, 255, 0.1)' fill='none' stroke-width='0.5'/%3E%3C/svg%3E")`,
          backgroundSize: '30px 30px',
          opacity: 0.2,
          zIndex: 0,
        }} />

        {/* Logo/Shield in the top area */}
        <div 
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            marginBottom: '32px',
            padding: '0 16px',
            position: 'relative',
            zIndex: 1,
          }}
        >
          <div
            style={{
              width: '48px',
              height: '48px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              borderRadius: '12px',
              color: '#00e5ff',
              background: 'rgba(0, 229, 255, 0.1)',
              border: '1px solid rgba(0, 229, 255, 0.3)',
              boxShadow: '0 0 20px rgba(0, 229, 255, 0.3)',
              transition: 'all 0.2s ease',
              marginBottom: '10px',
              backdropFilter: 'blur(5px)',
              WebkitBackdropFilter: 'blur(5px)',
            }}
          >
            <ShieldIcon />
          </div>
          {!collapsed && (
            <span style={{ 
              fontWeight: 'bold',
              fontSize: '16px',
              textAlign: 'center',
              background: 'linear-gradient(90deg, #ffffff, #00e5ff)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              textShadow: '0 0 10px rgba(0, 229, 255, 0.5)',
            }}>
              GuardBear
            </span>
          )}
        </div>

        {/* Main Navigation Section */}
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '12px',
          flex: 1,
          justifyContent: 'center',
          position: 'relative',
          zIndex: 1,
          width: '100%',
        }}>
          {/* Dashboard/Home Icon */}
          <div 
            onClick={() => handleNavClick('dashboard', ROUTES.HOME)}
            style={getNavItemStyle('dashboard')}
          >
            <HomeIcon />
            {!collapsed && (
              <span style={{ 
                marginLeft: '10px',
                fontSize: '14px',
                display: 'inline-block',
                transition: 'opacity 0.2s ease',
                opacity: collapsed ? 0 : 1
              }}>
                Dashboard
              </span>
            )}
          </div>

          {/* Explore Navigation */}
          <div style={{ position: 'relative', width: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <ExploreNavItem
              isCollapsed={!!collapsed}
              isExpanded={!!exploreExpanded}
              onToggle={handleExploreToggle}
              activeSubItem={activeNav === 'explore' ? activeExploreItem : undefined}
              isActive={activeNav === 'explore'}
            />
            {activeNav === 'explore' && (
              <ExploreDropdown
                isExpanded={!!exploreExpanded}
                isCollapsed={!!collapsed}
                activeItem={activeNav === 'explore' ? activeExploreItem : undefined}
                onItemClick={handleExploreItemClick}
              />
            )}
          </div>

          {/* Endpoint Security Navigation */}
          <div style={{ position: 'relative', width: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <EndpointSecurityNavItem
              isCollapsed={!!collapsed}
              isExpanded={!!endpointSecurityExpanded}
              onToggle={handleEndpointSecurityToggle}
              activeSubItem={activeNav === 'endpoint-security' ? activeEndpointSecurityItem : undefined}
              isActive={activeNav === 'endpoint-security'}
            />
            {activeNav === 'endpoint-security' && (
              <EndpointSecurityDropdown
                isExpanded={!!endpointSecurityExpanded}
                isCollapsed={!!collapsed}
                activeItem={activeNav === 'endpoint-security' ? activeEndpointSecurityItem : undefined}
                onItemClick={handleEndpointSecurityItemClick}
              />
            )}
          </div>

          {/* Grid Icon */}
          <div 
            onClick={() => handleNavClick('grid', ROUTES.GRID)}
            style={getNavItemStyle('grid')}
          >
            <GridIcon />
            {!collapsed && (
              <span style={{ 
                marginLeft: '10px',
                fontSize: '14px',
                display: 'inline-block',
                transition: 'opacity 0.2s ease',
                opacity: collapsed ? 0 : 1
              }}>
                Grid View
              </span>
            )}
          </div>

          {/* Clock/History Icon */}
          <div 
            onClick={() => handleNavClick('history', ROUTES.HISTORY)}
            style={getNavItemStyle('history')}
          >
            <ClockIcon />
            {!collapsed && (
              <span style={{ 
                marginLeft: '10px',
                fontSize: '14px',
                display: 'inline-block',
                transition: 'opacity 0.2s ease',
                opacity: collapsed ? 0 : 1
              }}>
                History
              </span>
            )}
          </div>

          {/* Files/Document Icon */}
          <div 
            onClick={() => handleNavClick('files', ROUTES.FILES)}
            style={getNavItemStyle('files')}
          >
            <FileIcon />
            {!collapsed && (
              <span style={{ 
                marginLeft: '10px',
                fontSize: '14px',
                display: 'inline-block',
                transition: 'opacity 0.2s ease',
                opacity: collapsed ? 0 : 1
              }}>
                Files
              </span>
            )}
          </div>

          {/* User/Profile Icon */}
          <div 
            onClick={() => handleNavClick('profile', ROUTES.PROFILE)}
            style={getNavItemStyle('profile')}
          >
            <UserIcon />
            {!collapsed && (
              <span style={{ 
                marginLeft: '10px',
                fontSize: '14px',
                display: 'inline-block',
                transition: 'opacity 0.2s ease',
                opacity: collapsed ? 0 : 1
              }}>
                Profile
              </span>
            )}
          </div>
        </div>

        {/* Bottom Section with Settings and User Profile */}
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          marginTop: 'auto',
          paddingTop: '15px',
          position: 'relative',
          zIndex: 1,
          width: '100%',
        }}>
          {/* Settings Gear */}
          <div 
            onClick={() => handleNavClick('settings', ROUTES.SETTINGS)}
            style={getNavItemStyle('settings')}
          >
            <SettingsIcon />
            {!collapsed && (
              <span style={{ 
                marginLeft: '10px',
                fontSize: '14px',
                display: 'inline-block',
                transition: 'opacity 0.2s ease',
                opacity: collapsed ? 0 : 1
              }}>
                Settings
              </span>
            )}
          </div>

          {/* User Profile at bottom */}
          <div style={{
            width: '32px',
            height: '32px',
            overflow: 'hidden',
            borderRadius: '50%',
            marginBottom: '10px',
            border: '2px solid #00e5ff',
            cursor: 'pointer',
            boxShadow: '0 0 5px rgba(0, 229, 255, 0.5)'
          }}>
            <img 
              src="/avatar-placeholder.png" 
              alt="User Profile" 
              style={{ width: '100%', height: '100%', objectFit: 'cover' }}
              onError={(e) => {
                // Fallback when image doesn't load
                e.currentTarget.src = 'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2300e5ff"><path d="M12 12a5 5 0 1 0 0-10 5 5 0 0 0 0 10z" /><path d="M12 14c-6.1 0-10 4-10 8h20c0-4-3.9-8-10-8z" /></svg>';
              }}
            />
          </div>

          {!collapsed && (
            <div style={{
              textAlign: 'center',
              fontSize: '12px',
              color: 'rgba(255, 255, 255, 0.7)',
              marginBottom: '5px'
            }}>
              Security Admin
            </div>
          )}

          {/* Timestamp visible at bottom of the nav */}
          <div style={{ 
            fontSize: '10px', 
            color: 'rgba(255, 255, 255, 0.4)',
            marginTop: '5px' 
          }}>
            {new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false })}
          </div>
        </div>
      </div>

      {/* Toggle Button */}
      <div 
        onClick={toggleSidebar}
        onMouseEnter={() => setIsHovering(true)}
        onMouseLeave={() => setIsHovering(false)}
        style={{
          position: 'absolute',
          top: '18px',
          left: collapsed ? '68px' : '188px',
          zIndex: 1000,
          width: '24px',
          height: '24px',
          borderRadius: '50%',
          background: 'rgba(0, 229, 255, 0.1)',
          border: '1px solid rgba(0, 229, 255, 0.3)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          cursor: 'pointer',
          color: '#00e5ff',
          boxShadow: isHovering ? '0 0 10px rgba(0, 229, 255, 0.5)' : 'none',
          transition: 'all 0.2s ease',
        }}
      >
        <svg
          width="14"
          height="14"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          style={{ 
            transform: collapsed ? 'rotate(0deg)' : 'rotate(180deg)',
            transition: 'transform 0.3s ease'
          }}
        >
          <polyline points="15 18 9 12 15 6"></polyline>
        </svg>
      </div>
    </>
  );
};

export default NavigationSidebar;