import React, { Component, ErrorInfo, ReactNode } from 'react';

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

/**
 * Error Boundary component.
 * 
 * This component catches JavaScript errors anywhere in its child component tree,
 * logs those errors, and displays a fallback UI instead of the component tree that crashed.
 */
class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to an error reporting service
    console.error('Error caught by ErrorBoundary:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });
  }

  render(): ReactNode {
    if (this.state.hasError) {
      // Render custom fallback UI if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }
      
      // Default fallback UI
      return (
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          width: '100vw',
          padding: '20px',
          background: 'linear-gradient(135deg, #0a0e17 0%, #121a2c 100%)',
          color: 'white',
          textAlign: 'center',
        }}>
          <div style={{
            width: '80px',
            height: '80px',
            borderRadius: '50%',
            background: 'rgba(255, 99, 132, 0.1)',
            border: '2px solid rgba(255, 99, 132, 0.5)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: '20px',
            boxShadow: '0 0 20px rgba(255, 99, 132, 0.3)',
          }}>
            <svg
              width="40"
              height="40"
              viewBox="0 0 24 24"
              fill="none"
              stroke="rgba(255, 99, 132, 1)"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="8" x2="12" y2="12"></line>
              <line x1="12" y1="16" x2="12.01" y2="16"></line>
            </svg>
          </div>
          
          <h1 style={{ 
            fontSize: '28px', 
            fontWeight: 600, 
            marginBottom: '16px',
            color: 'rgba(255, 99, 132, 1)',
          }}>
            Something went wrong
          </h1>
          
          <p style={{ 
            fontSize: '16px', 
            color: 'rgba(255, 255, 255, 0.7)',
            marginBottom: '24px',
            maxWidth: '600px',
          }}>
            An error occurred while rendering this page. Please try refreshing the page or contact support if the problem persists.
          </p>
          
          <button
            onClick={() => window.location.reload()}
            style={{
              background: 'rgba(255, 99, 132, 0.1)',
              border: '1px solid rgba(255, 99, 132, 0.5)',
              borderRadius: '4px',
              padding: '10px 20px',
              color: 'rgba(255, 99, 132, 1)',
              fontSize: '16px',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
            }}
          >
            Refresh Page
          </button>
          
          {/* Error details (only in development) */}
          {process.env.NODE_ENV === 'development' && this.state.error && (
            <div style={{
              marginTop: '40px',
              padding: '20px',
              background: 'rgba(0, 0, 0, 0.2)',
              borderRadius: '8px',
              width: '100%',
              maxWidth: '800px',
              overflow: 'auto',
              textAlign: 'left',
            }}>
              <h3 style={{ color: 'rgba(255, 99, 132, 1)', marginBottom: '10px' }}>
                Error Details (Development Only)
              </h3>
              <p style={{ color: 'rgba(255, 255, 255, 0.9)', marginBottom: '10px' }}>
                <strong>Error:</strong> {this.state.error.toString()}
              </p>
              {this.state.errorInfo && (
                <pre style={{ 
                  color: 'rgba(255, 255, 255, 0.7)',
                  fontSize: '12px',
                  overflow: 'auto',
                  maxHeight: '300px',
                  padding: '10px',
                  background: 'rgba(0, 0, 0, 0.3)',
                  borderRadius: '4px',
                }}>
                  {this.state.errorInfo.componentStack}
                </pre>
              )}
            </div>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;