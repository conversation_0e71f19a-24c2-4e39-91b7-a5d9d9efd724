/**
 * Configuration Assessment Reducer
 * 
 * This file contains the reducer function for the Configuration Assessment context.
 * Optimized for performance with memoization and efficient state updates.
 */

import { 
  ConfigurationAssessmentState, 
  ConfigurationAssessmentAction 
} from './ConfigurationAssessmentContext';
import { filterConfigurationEntries, sortConfigurationEntries } from '../utils/configurationUtils';
import { createPerformanceTracker } from '../utils/performanceTracking';
import { 
  createPerformanceComparisonTracker, 
  OperationCategory 
} from '../utils/performanceComparison';

// Create performance trackers
const performanceTracker = createPerformanceTracker('ConfigurationAssessmentContext');
const comparisonTracker = createPerformanceComparisonTracker(
  'ConfigurationAssessmentContext',
  'DiscoverContext'
);

/**
 * Configuration Assessment Reducer function
 * @param state Current state
 * @param action Action to process
 * @returns New state
 */
export function configurationAssessmentReducer(
  state: ConfigurationAssessmentState,
  action: ConfigurationAssessmentAction
): ConfigurationAssessmentState {
  switch (action.type) {
    case 'SET_SEARCH_QUERY': {
      // Skip processing if the query hasn't changed
      if (state.searchQuery === action.payload) {
        return state;
      }
      
      // Measure filtering performance
      const filteredData = performanceTracker.trackOperation('filterBySearchQuery', () => 
        filterConfigurationEntries(state.configData, {
          query: action.payload,
          filters: state.appliedFilters,
          timeRange: state.timeRange,
          sort: state.sort || undefined,
        })
      , 'filtering');
      
      // Compare with Discover page
      comparisonTracker.compareOperation(
        'filterBySearchQuery',
        'filterBySearchQuery',
        OperationCategory.FILTERING
      );
      
      return {
        ...state,
        searchQuery: action.payload,
        filteredData,
        pagination: {
          ...state.pagination,
          currentPage: 0, // Reset to first page when search changes
          totalItems: filteredData.length,
        },
        processingOperation: false,
      };
    }

    case 'SET_TIME_RANGE': {
      // Skip processing if the time range hasn't changed
      if (state.timeRange.start.getTime() === action.payload.start.getTime() && 
          state.timeRange.end.getTime() === action.payload.end.getTime()) {
        return state;
      }
      
      // Measure filtering performance
      const filteredData = performanceTracker.trackOperation('filterByTimeRange', () => 
        filterConfigurationEntries(state.configData, {
          query: state.searchQuery,
          filters: state.appliedFilters,
          timeRange: action.payload,
          sort: state.sort || undefined,
        })
      , 'filtering');
      
      // Compare with Discover page
      comparisonTracker.compareOperation(
        'filterByTimeRange',
        'filterByTimeRange',
        OperationCategory.FILTERING
      );
      
      return {
        ...state,
        timeRange: action.payload,
        filteredData,
        pagination: {
          ...state.pagination,
          currentPage: 0, // Reset to first page when time range changes
          totalItems: filteredData.length,
        },
        processingOperation: false,
      };
    }

    case 'ADD_FILTER': {
      // Check if filter already exists
      const filterExists = state.appliedFilters.some(
        filter => 
          filter.field === action.payload.field && 
          filter.operator === action.payload.operator && 
          filter.value === action.payload.value
      );
      
      if (filterExists) {
        return state;
      }
      
      const newFilters = [...state.appliedFilters, action.payload];
      
      // Measure filtering performance
      const filteredData = performanceTracker.trackOperation('filterWithAddedFilter', () => 
        filterConfigurationEntries(state.configData, {
          query: state.searchQuery,
          filters: newFilters,
          timeRange: state.timeRange,
          sort: state.sort || undefined,
        })
      , 'filtering');
      
      // Compare with Discover page
      comparisonTracker.compareOperation(
        'filterWithAddedFilter',
        'filterWithAddedFilter',
        OperationCategory.FILTERING
      );
      
      return {
        ...state,
        appliedFilters: newFilters,
        filteredData,
        pagination: {
          ...state.pagination,
          currentPage: 0, // Reset to first page when filters change
          totalItems: filteredData.length,
        },
        processingOperation: false,
      };
    }

    case 'REMOVE_FILTER': {
      // Check if filter exists
      const filterExists = state.appliedFilters.some(
        filter => filter.field.toString() === action.payload
      );
      
      if (!filterExists) {
        return state;
      }
      
      const updatedFilters = state.appliedFilters.filter(
        filter => filter.field.toString() !== action.payload
      );
      
      // Measure filtering performance
      const filteredData = performanceTracker.trackOperation('filterWithRemovedFilter', () => 
        filterConfigurationEntries(state.configData, {
          query: state.searchQuery,
          filters: updatedFilters,
          timeRange: state.timeRange,
          sort: state.sort || undefined,
        })
      , 'filtering');
      
      // Compare with Discover page
      comparisonTracker.compareOperation(
        'filterWithRemovedFilter',
        'filterWithRemovedFilter',
        OperationCategory.FILTERING
      );
      
      return {
        ...state,
        appliedFilters: updatedFilters,
        filteredData,
        pagination: {
          ...state.pagination,
          currentPage: 0, // Reset to first page when filters change
          totalItems: filteredData.length,
        },
        processingOperation: false,
      };
    }

    case 'TOGGLE_FIELD': {
      const fieldExists = state.selectedFields.includes(action.payload);
      const selectedFields = fieldExists
        ? state.selectedFields.filter(field => field !== action.payload)
        : [...state.selectedFields, action.payload];

      return {
        ...state,
        selectedFields,
      };
    }

    case 'SET_CONFIG_DATA': {
      // Skip if data is the same (reference equality)
      if (state.configData === action.payload && state.configData.length > 0) {
        return {
          ...state,
          isLoading: false,
          processingOperation: false,
        };
      }
      
      // Measure filtering performance
      const filteredData = performanceTracker.trackOperation('filterNewConfigData', () => 
        filterConfigurationEntries(action.payload, {
          query: state.searchQuery,
          filters: state.appliedFilters,
          timeRange: state.timeRange,
          sort: state.sort || undefined,
        })
      , 'filtering');
      
      // Compare with Discover page
      comparisonTracker.compareOperation(
        'filterNewConfigData',
        'filterNewConfigData',
        OperationCategory.FILTERING
      );
      
      return {
        ...state,
        configData: action.payload,
        filteredData,
        pagination: {
          ...state.pagination,
          totalItems: filteredData.length,
          currentPage: 0, // Reset to first page when data changes
        },
        isLoading: false,
        processingOperation: false,
      };
    }

    case 'SET_LOADING':
      // Skip if loading state is the same
      if (state.isLoading === action.payload) {
        return state;
      }
      
      return {
        ...state,
        isLoading: action.payload,
        // Set processing operation flag when loading starts
        processingOperation: action.payload ? true : state.processingOperation,
      };
      
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isLoading: false,
        processingOperation: false,
      };

    case 'SET_AUTO_REFRESH':
      // Skip if auto refresh state is the same
      if (state.autoRefresh === action.payload) {
        return state;
      }
      
      return {
        ...state,
        autoRefresh: action.payload,
      };

    case 'SET_REFRESH_INTERVAL':
      // Skip if refresh interval is the same
      if (state.refreshInterval === action.payload) {
        return state;
      }
      
      return {
        ...state,
        refreshInterval: action.payload,
      };

    case 'SET_CURRENT_PAGE':
      // Skip if current page is the same
      if (state.pagination.currentPage === action.payload) {
        return state;
      }
      
      return {
        ...state,
        pagination: {
          ...state.pagination,
          currentPage: action.payload,
        },
        processingOperation: false,
      };

    case 'SET_PAGE_SIZE': {
      const newPageSize = action.payload;
      
      // Skip if page size is the same
      if (state.pagination.pageSize === newPageSize) {
        return state;
      }
      
      return {
        ...state,
        pagination: {
          ...state.pagination,
          pageSize: newPageSize,
          currentPage: 0, // Reset to first page when page size changes
        },
        processingOperation: false,
      };
    }

    case 'SET_SORT': {
      const newSort = action.payload;
      
      // Skip if sort is the same
      if (
        (!newSort && !state.sort) || 
        (state.sort && newSort && 
         state.sort.field === newSort.field && 
         state.sort.direction === newSort.direction)
      ) {
        return {
          ...state,
          processingOperation: false,
        };
      }
      
      let sortedData = [...state.filteredData];
      
      if (newSort) {
        // Measure sorting performance
        sortedData = performanceTracker.trackOperation('sortData', () => 
          sortConfigurationEntries(sortedData, newSort)
        , 'sorting');
        
        // Compare with Discover page
        comparisonTracker.compareOperation(
          'sortData',
          'sortData',
          OperationCategory.SORTING
        );
      }
      
      return {
        ...state,
        sort: newSort,
        filteredData: sortedData,
        processingOperation: false,
      };
    }

    case 'SET_PROCESSING_OPERATION':
      return {
        ...state,
        processingOperation: action.payload,
      };

    default:
      return state;
  }
}

/**
 * Get initial state for the Configuration Assessment context
 * @returns Initial state
 */
export function getInitialState(): ConfigurationAssessmentState {
  return {
    searchQuery: '',
    timeRange: {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      end: new Date(),
      preset: 'last-30d',
    },
    selectedFields: [
      'rule.id',
      'rule.description',
      'check.title',
      'result',
      'score',
      'component',
      'agent.name',
      'timestamp',
    ],
    appliedFilters: [],
    configData: [],
    filteredData: [],
    isLoading: true,
    error: null,
    autoRefresh: false,
    refreshInterval: 60000, // 1 minute
    pagination: {
      currentPage: 0,
      pageSize: 10,
      totalItems: 0,
    },
    sort: null,
    processingOperation: false, // Flag for ongoing operations
  };
}