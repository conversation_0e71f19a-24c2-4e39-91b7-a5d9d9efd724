import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { DiscoverState, DiscoverAction, LogEntry } from '../types/discover';
import { DiscoverUtils } from '../utils/discoverUtils';
import { sampleLogData } from '../data/sampleLogData';

// Initial state
const initialState: DiscoverState = DiscoverUtils.getInitialState();

// Create context
const DiscoverContext = createContext<{
  state: DiscoverState;
  dispatch: React.Dispatch<DiscoverAction>;
} | undefined>(undefined);

// Reducer function
function discoverReducer(state: DiscoverState, action: DiscoverAction): DiscoverState {
  switch (action.type) {
    case 'SET_SEARCH_QUERY':
      return {
        ...state,
        searchQuery: action.payload,
        filteredData: DiscoverUtils.filterLogData(
          state.logData,
          action.payload,
          state.appliedFilters,
          state.timeRange
        ),
      };
      
    case 'SET_TIME_RANGE':
      return {
        ...state,
        timeRange: action.payload,
        filteredData: DiscoverUtils.filterLogData(
          state.logData,
          state.searchQuery,
          state.appliedFilters,
          action.payload
        ),
        histogramData: DiscoverUtils.generateHistogramData(
          DiscoverUtils.filterLogData(
            state.logData,
            state.searchQuery,
            state.appliedFilters,
            action.payload
          ),
          action.payload
        ),
      };
      
    case 'ADD_FILTER':
      const newFilters = [...state.appliedFilters, action.payload];
      return {
        ...state,
        appliedFilters: newFilters,
        filteredData: DiscoverUtils.filterLogData(
          state.logData,
          state.searchQuery,
          newFilters,
          state.timeRange
        ),
      };
      
    case 'REMOVE_FILTER':
      const updatedFilters = state.appliedFilters.filter(filter => filter.field !== action.payload);
      return {
        ...state,
        appliedFilters: updatedFilters,
        filteredData: DiscoverUtils.filterLogData(
          state.logData,
          state.searchQuery,
          updatedFilters,
          state.timeRange
        ),
      };
      
    case 'TOGGLE_FIELD':
      const fieldExists = state.selectedFields.includes(action.payload);
      const selectedFields = fieldExists
        ? state.selectedFields.filter(field => field !== action.payload)
        : [...state.selectedFields, action.payload];
      
      return {
        ...state,
        selectedFields,
      };
      
    case 'SET_LOG_DATA':
      return {
        ...state,
        logData: action.payload,
        filteredData: DiscoverUtils.filterLogData(
          action.payload,
          state.searchQuery,
          state.appliedFilters,
          state.timeRange
        ),
        histogramData: DiscoverUtils.generateHistogramData(
          DiscoverUtils.filterLogData(
            action.payload,
            state.searchQuery,
            state.appliedFilters,
            state.timeRange
          ),
          state.timeRange
        ),
        isLoading: false,
      };
      
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
      
    case 'SET_AUTO_REFRESH':
      return {
        ...state,
        autoRefresh: action.payload,
      };
      
    case 'SET_REFRESH_INTERVAL':
      return {
        ...state,
        refreshInterval: action.payload,
      };
      
    case 'SET_CURRENT_PAGE':
      return {
        ...state,
        pagination: {
          ...state.pagination,
          currentPage: action.payload,
        },
      };
      
    case 'SET_PAGE_SIZE':
      return {
        ...state,
        pagination: {
          ...state.pagination,
          pageSize: action.payload,
          currentPage: 1, // Reset to first page when changing page size
        },
      };
      
    default:
      return state;
  }
}

// Provider component
interface DiscoverProviderProps {
  children: ReactNode;
  initialData?: LogEntry[];
}

export const DiscoverProvider: React.FC<DiscoverProviderProps> = ({ 
  children, 
  initialData = sampleLogData 
}) => {
  const [state, dispatch] = useReducer(discoverReducer, {
    ...initialState,
    isLoading: true,
  });
  
  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      try {
        // In a real app, this would be an API call
        // For now, we're using the sample data
        dispatch({ type: 'SET_LOG_DATA', payload: initialData });
      } catch (error) {
        console.error('Error loading log data:', error);
        dispatch({ type: 'SET_LOG_DATA', payload: [] });
      }
    };
    
    loadData();
  }, [initialData]);
  
  // Set up auto-refresh if enabled
  useEffect(() => {
    if (!state.autoRefresh) return;
    
    const intervalId = setInterval(() => {
      // In a real app, this would fetch fresh data
      // For demo purposes, we'll just refresh with the same data
      dispatch({ type: 'SET_LOG_DATA', payload: state.logData });
    }, state.refreshInterval);
    
    return () => clearInterval(intervalId);
  }, [state.autoRefresh, state.refreshInterval, state.logData]);
  
  const value = { state, dispatch };
  
  return (
    <DiscoverContext.Provider value={value}>
      {children}
    </DiscoverContext.Provider>
  );
};

// Custom hook for using the context
export const useDiscover = () => {
  const context = useContext(DiscoverContext);
  
  if (context === undefined) {
    throw new Error('useDiscover must be used within a DiscoverProvider');
  }
  
  return context;
};

// Action creator functions for common operations
export const discoverActions = {
  setSearchQuery: (query: string): DiscoverAction => ({
    type: 'SET_SEARCH_QUERY',
    payload: query,
  }),
  
  setTimeRange: (from: Date, to: Date, preset?: string): DiscoverAction => ({
    type: 'SET_TIME_RANGE',
    payload: { from, to, preset: preset as any },
  }),
  
  addFilter: (field: string, value: any, operator: 'is' | 'is not' | 'exists' | 'does not exist' | 'contains' = 'is'): DiscoverAction => ({
    type: 'ADD_FILTER',
    payload: DiscoverUtils.createFilter(field, value, operator),
  }),
  
  removeFilter: (field: string): DiscoverAction => ({
    type: 'REMOVE_FILTER',
    payload: field,
  }),
  
  toggleField: (field: string): DiscoverAction => ({
    type: 'TOGGLE_FIELD',
    payload: field,
  }),
  
  setAutoRefresh: (enabled: boolean): DiscoverAction => ({
    type: 'SET_AUTO_REFRESH',
    payload: enabled,
  }),
  
  setRefreshInterval: (interval: number): DiscoverAction => ({
    type: 'SET_REFRESH_INTERVAL',
    payload: interval,
  }),
  
  refresh: (): DiscoverAction => ({
    type: 'SET_LOADING',
    payload: true,
  }),
  
  setCurrentPage: (page: number): DiscoverAction => ({
    type: 'SET_CURRENT_PAGE',
    payload: page,
  }),
  
  setPageSize: (size: number): DiscoverAction => ({
    type: 'SET_PAGE_SIZE',
    payload: size,
  }),
};
