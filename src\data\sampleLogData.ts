import { LogEntry } from '../types/discover';

// Sample data generator for realistic security log entries
export class SampleDataGenerator {
  private static readonly AGENTS = [
    { id: '001', name: 'web-server-01', ip: '************' },
    { id: '002', name: 'db-server-01', ip: '************' },
    { id: '003', name: 'mail-server-01', ip: '************' },
    { id: '004', name: 'workstation-01', ip: '************0' },
    { id: '005', name: 'firewall-01', ip: '***********' },
  ];

  private static readonly RULE_TEMPLATES = [
    { id: 5501, description: 'Login session opened', level: 3, groups: ['authentication', 'pci_dss_10.2.5'] },
    { id: 5502, description: 'Login session closed', level: 3, groups: ['authentication', 'pci_dss_10.2.5'] },
    { id: 5503, description: 'User login failed', level: 5, groups: ['authentication_failed', 'pci_dss_10.2.4'] },
    { id: 5504, description: 'Multiple authentication failures', level: 8, groups: ['authentication_failures', 'pci_dss_11.4'] },
    { id: 1002, description: 'Unknown problem somewhere in the system', level: 2, groups: ['syslog', 'errors'] },
    { id: 2501, description: 'User missed the password more than one time', level: 5, groups: ['authentication_failed'] },
    { id: 31151, description: 'Connection to web server', level: 3, groups: ['web', 'access'] },
    { id: 31106, description: 'Invalid URL access', level: 6, groups: ['web', 'attack'] },
    { id: 40101, description: 'FTP Authentication success', level: 3, groups: ['ftp', 'authentication_success'] },
    { id: 40111, description: 'FTP Authentication failed', level: 5, groups: ['ftp', 'authentication_failed'] },
  ];

  private static readonly LOCATIONS = [
    '/var/log/auth.log',
    '/var/log/apache2/access.log',
    '/var/log/apache2/error.log',
    '/var/log/mysql/error.log',
    '/var/log/mail.log',
    '/var/log/syslog',
    'EventChannel',
    'Security',
  ];

  private static readonly DECODERS = [
    { name: 'sshd' },
    { name: 'apache-access' },
    { name: 'apache-error' },
    { name: 'mysql' },
    { name: 'postfix' },
    { name: 'syslog' },
    { name: 'windows-security' },
  ];

  private static readonly MESSAGE_TEMPLATES = [
    'Accepted password for {user} from {ip} port {port} ssh2',
    'Failed password for {user} from {ip} port {port} ssh2',
    'Connection from {ip} port {port} on {local_ip} port 22',
    'User {user} from {ip} not allowed because not listed in AllowUsers',
    '{ip} - - [{timestamp}] "GET {path} HTTP/1.1" {status} {size}',
    '{ip} - - [{timestamp}] "POST {path} HTTP/1.1" {status} {size}',
    'Access denied with code 403 (phase 2). Pattern match "{pattern}" at REQUEST_URI',
    'MySQL server has gone away',
    'Connection refused by {service}',
    'Service {service} started successfully',
  ];

  private static readonly USERS = ['admin', 'root', 'user1', 'john.doe', 'service_account', 'backup_user'];
  private static readonly IPS = ['************', '*********', '************', '************', '*************'];
  private static readonly PATHS = ['/login', '/admin', '/api/users', '/dashboard', '/upload', '/config'];

  static generateSampleData(
    count: number = 1000,
    timeRangeInDays: number = 30,
    includeTypes: string[] = ['authentication', 'web', 'system', 'network']
  ): LogEntry[] {
    const logs: LogEntry[] = [];
    const now = new Date();

    // Filter rules based on includeTypes
    const filteredRules = this.RULE_TEMPLATES.filter(rule =>
      rule.groups.some(group =>
        includeTypes.some(type => group.toLowerCase().includes(type.toLowerCase()))
      )
    );

    // Use all rules if filtering resulted in empty array
    const rulesToUse = filteredRules.length > 0 ? filteredRules : this.RULE_TEMPLATES;

    for (let i = 0; i < count; i++) {
      // Generate timestamp within specified time range with realistic distribution
      const daysAgo = Math.random() * timeRangeInDays;
      const timestamp = new Date(now.getTime() - (daysAgo * 24 * 60 * 60 * 1000));

      const agent = this.getRandomItem(this.AGENTS);
      const rule = this.getRandomItem(rulesToUse);
      const location = this.getRandomItem(this.LOCATIONS);
      const decoder = this.getRandomItem(this.DECODERS);

      const level = this.getLogLevel(rule.level);
      const message = this.generateMessage(rule);

      const logEntry: LogEntry = {
        id: `log_${i}_${Date.now()}`,
        timestamp,
        source: agent.name,
        message,
        level,
        agent,
        rule,
        location,
        decoder,
        data: this.generateAdditionalFields(rule, agent),
      };

      logs.push(logEntry);
    }

    // Sort by timestamp (newest first)
    return logs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  private static getRandomItem<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)];
  }

  private static getLogLevel(ruleLevel: number): 'info' | 'warning' | 'error' | 'critical' {
    if (ruleLevel <= 3) return 'info';
    if (ruleLevel <= 6) return 'warning';
    if (ruleLevel <= 9) return 'error';
    return 'critical';
  }

  private static generateMessage(rule: typeof SampleDataGenerator.RULE_TEMPLATES[0]): string {
    const template = this.getRandomItem(this.MESSAGE_TEMPLATES);
    const user = this.getRandomItem(this.USERS);
    const ip = this.getRandomItem(this.IPS);
    const port = Math.floor(Math.random() * 65535) + 1;
    const path = this.getRandomItem(this.PATHS);
    const status = this.getRandomItem([200, 301, 404, 403, 500]);
    const size = Math.floor(Math.random() * 10000) + 100;

    return template
      .replace('{user}', user)
      .replace('{ip}', ip)
      .replace('{port}', port.toString())
      .replace('{local_ip}', '************')
      .replace('{path}', path)
      .replace('{status}', status.toString())
      .replace('{size}', size.toString())
      .replace('{service}', 'apache2')
      .replace('{pattern}', 'SQL injection attempt')
      .replace('{timestamp}', new Date().toISOString());
  }

  private static generateAdditionalFields(rule: typeof SampleDataGenerator.RULE_TEMPLATES[0], agent: typeof SampleDataGenerator.AGENTS[0]): Record<string, any> {
    const fields: Record<string, any> = {
      'rule.groups': rule.groups,
      'agent.os': this.getRandomItem(['Linux', 'Windows', 'macOS']),
      'agent.version': '4.3.10',
      'manager.name': 'wazuh-manager',
    };

    // Add rule-specific fields
    if (rule.groups.includes('authentication')) {
      fields['auth.user'] = this.getRandomItem(this.USERS);
      fields['auth.method'] = this.getRandomItem(['password', 'key', 'certificate']);
      fields['auth.success'] = rule.id !== 5503 && rule.id !== 5504;
      fields['auth.attempts'] = rule.id === 5504 ? Math.floor(Math.random() * 5) + 3 : 1;
    }

    if (rule.groups.includes('web')) {
      fields['http.method'] = this.getRandomItem(['GET', 'POST', 'PUT', 'DELETE']);
      fields['http.status'] = this.getRandomItem([200, 301, 404, 403, 500]);
      fields['http.user_agent'] = this.getRandomItem([
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
        'Mozilla/5.0 (compatible; SecurityScanner/1.0)',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36'
      ]);
      fields['http.url'] = this.getRandomItem(this.PATHS);
      fields['http.bytes'] = Math.floor(Math.random() * 10000) + 100;
    }

    if (rule.groups.includes('attack')) {
      fields['attack.type'] = this.getRandomItem(['sql_injection', 'xss', 'brute_force', 'directory_traversal']);
      fields['attack.severity'] = this.getRandomItem(['low', 'medium', 'high', 'critical']);
      fields['attack.source_ip'] = this.getRandomItem([...this.IPS, '*************', '*************', '************']);
      fields['attack.blocked'] = Math.random() > 0.3;
    }

    // Add system-specific fields
    if (rule.groups.includes('syslog')) {
      fields['system.process'] = this.getRandomItem(['kernel', 'systemd', 'cron', 'sshd', 'apache2']);
      fields['system.pid'] = Math.floor(Math.random() * 10000) + 1;
      fields['system.status'] = this.getRandomItem(['success', 'failure', 'error', 'warning']);
    }

    // Add network-specific fields for certain rules
    if (rule.level > 5) {
      fields['network.protocol'] = this.getRandomItem(['tcp', 'udp', 'icmp']);
      fields['network.source_port'] = Math.floor(Math.random() * 65535) + 1;
      fields['network.destination_port'] = this.getRandomItem([22, 80, 443, 3306, 25, 53]);
      fields['network.packets'] = Math.floor(Math.random() * 100) + 1;
    }

    return fields;
  }
}

// Export pre-generated sample data with different sizes for testing
export const sampleLogData = SampleDataGenerator.generateSampleData(1000);
export const smallSampleLogData = SampleDataGenerator.generateSampleData(50);
export const largeSampleLogData = SampleDataGenerator.generateSampleData(5000);

// Export a function to generate data with specific parameters
export function generateCustomSampleData(
  count: number = 1000,
  timeRangeInDays: number = 30,
  includeTypes: string[] = ['authentication', 'web', 'system', 'network']
): LogEntry[] {
  return SampleDataGenerator.generateSampleData(count, timeRangeInDays, includeTypes);
}

/**
 * Generate sample data with specific time distribution patterns
 * Useful for testing histogram visualizations
 */
export function generatePatternedData(
  pattern: 'spike' | 'gradual-increase' | 'periodic' | 'random' = 'random',
  count: number = 1000,
  timeRangeInDays: number = 7
): LogEntry[] {
  const now = new Date();
  const baseData = SampleDataGenerator.generateSampleData(count, timeRangeInDays);

  // Override timestamps based on pattern
  switch (pattern) {
    case 'spike':
      // Create a spike in the middle of the time range
      return baseData.map((log, index) => {
        const middleDay = timeRangeInDays / 2;
        let daysAgo: number;

        if (index < count * 0.1) {
          // 10% of logs before spike
          daysAgo = Math.random() * middleDay + middleDay;
        } else if (index < count * 0.7) {
          // 60% of logs during spike (within 4 hours)
          daysAgo = middleDay + (Math.random() * 4 / 24);
        } else {
          // 30% of logs after spike
          daysAgo = Math.random() * middleDay;
        }

        return {
          ...log,
          timestamp: new Date(now.getTime() - (daysAgo * 24 * 60 * 60 * 1000))
        };
      }).sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    case 'gradual-increase':
      // Create a gradual increase in log frequency
      return baseData.map((log, index) => {
        // More logs as we get closer to now
        const position = index / count;
        const daysAgo = timeRangeInDays * Math.pow(position, 2);

        return {
          ...log,
          timestamp: new Date(now.getTime() - (daysAgo * 24 * 60 * 60 * 1000))
        };
      }).sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    case 'periodic':
      // Create periodic spikes (e.g., daily patterns)
      return baseData.map((log, index) => {
        const day = Math.floor(index / (count / timeRangeInDays));
        const dayPosition = (index % (count / timeRangeInDays)) / (count / timeRangeInDays);

        // More logs during "business hours"
        let hourOfDay: number;
        if (dayPosition < 0.1) {
          hourOfDay = Math.random() * 6; // 0-6 AM (10% of logs)
        } else if (dayPosition < 0.7) {
          hourOfDay = 9 + Math.random() * 8; // 9 AM - 5 PM (60% of logs)
        } else {
          hourOfDay = 17 + Math.random() * 7; // 5 PM - 12 AM (30% of logs)
        }

        const daysAgo = timeRangeInDays - day - (hourOfDay / 24);

        return {
          ...log,
          timestamp: new Date(now.getTime() - (daysAgo * 24 * 60 * 60 * 1000))
        };
      }).sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    default:
      // Random distribution (already handled by base generator)
      return baseData;
  }
}

/**
 * Generate sample data focused on specific security scenarios
 */
export function generateScenarioData(
  scenario: 'brute-force' | 'data-exfiltration' | 'malware-infection' | 'privilege-escalation',
  count: number = 200
): LogEntry[] {
  // Base data with appropriate time range
  const baseData = SampleDataGenerator.generateSampleData(count, 1); // Last 24 hours

  // Customize data based on scenario
  switch (scenario) {
    case 'brute-force':
      // Simulate a brute force attack with failed logins followed by a successful one
      return baseData.map((log, index) => {
        const isFailedLogin = index < count - 5;
        const timestamp = new Date();

        // Set timestamps to create a sequence over the last hour
        timestamp.setMinutes(timestamp.getMinutes() - (60 * (1 - index / count)));

        return {
          ...log,
          timestamp,
          level: isFailedLogin ? 'warning' : 'critical',
          message: isFailedLogin
            ? `Failed password for root from ${log.agent.ip} port ${Math.floor(Math.random() * 10000) + 30000} ssh2`
            : `Accepted password for root from ${log.agent.ip} port ${Math.floor(Math.random() * 10000) + 30000} ssh2`,
          rule: {
            id: isFailedLogin ? 5503 : 5501,
            description: isFailedLogin ? 'User login failed' : 'Login session opened',
            level: isFailedLogin ? 5 : 3,
            groups: ['authentication', isFailedLogin ? 'authentication_failed' : 'authentication_success'],
          },
          data: {
            'auth.user': 'root',
            'auth.method': 'password',
            'auth.success': !isFailedLogin,
            'auth.attempts': isFailedLogin ? Math.floor(index / 10) + 1 : 1,
            'source_ip': log.agent.ip,
          }
        };
      });

    case 'data-exfiltration':
      // Simulate data exfiltration with suspicious outbound connections
      return baseData.map((log, index) => {
        const timestamp = new Date();
        const isDataTransfer = index > count * 0.7;

        // Set timestamps to create a sequence over the last 3 hours
        timestamp.setMinutes(timestamp.getMinutes() - (180 * (1 - index / count)));

        return {
          ...log,
          timestamp,
          level: isDataTransfer ? 'critical' : 'warning',
          message: isDataTransfer
            ? `Large outbound data transfer detected to ${['*************', '*************', '************'][index % 3]}`
            : `Connection established to unknown external IP ${['*************', '*************', '************'][index % 3]}`,
          rule: {
            id: isDataTransfer ? 6354 : 6350,
            description: isDataTransfer ? 'Data exfiltration detected' : 'Suspicious outbound connection',
            level: isDataTransfer ? 10 : 7,
            groups: ['data_exfiltration', 'network', 'attack'],
          },
          data: {
            'network.protocol': 'tcp',
            'network.source_port': Math.floor(Math.random() * 10000) + 50000,
            'network.destination_port': [443, 8080, 22][index % 3],
            'network.bytes_out': isDataTransfer ? Math.floor(Math.random() * 100000000) + 10000000 : Math.floor(Math.random() * 10000) + 1000,
            'network.bytes_in': Math.floor(Math.random() * 10000) + 1000,
            'attack.type': 'data_exfiltration',
            'attack.severity': isDataTransfer ? 'critical' : 'high',
          }
        };
      });

    default:
      return baseData;
  }
}