import { useMemo } from 'react';
import { useDiscover, discoverActions } from '../context/DiscoverContext';
import { DiscoverUtils } from '../utils/discoverUtils';
import { TimeRange } from '../types/discover';

/**
 * Hook for managing time range and histogram data
 */
export const useDiscoverTime = () => {
  const { state, dispatch } = useDiscover();
  const { timeRange, filteredData, histogramData } = state;
  
  // Get appropriate time interval based on current time range
  const timeInterval = useMemo(() => {
    return DiscoverUtils.getAppropriateTimeInterval(timeRange);
  }, [timeRange]);
  
  // Group logs by time interval for histogram
  const groupedData = useMemo(() => {
    return DiscoverUtils.groupLogsByTimeInterval(filteredData, timeInterval);
  }, [filteredData, timeInterval]);
  
  // Set time range from preset
  const setTimeRangePreset = (preset: string) => {
    const newRange = DiscoverUtils.createTimeRangeFromPreset(preset);
    dispatch(discoverActions.setTimeRange(newRange.from, newRange.to, preset));
  };
  
  // Set custom time range
  const setCustomTimeRange = (from: Date, to: Date) => {
    dispatch(discoverActions.setTimeRange(from, to, 'custom'));
  };
  
  // Format date for display
  const formatDate = (date: Date, includeTime: boolean = true) => {
    return DiscoverUtils.formatDate(date, includeTime);
  };
  
  // Get time range options for dropdown
  const timeRangeOptions = [
    { label: 'Last 15 minutes', value: 'last-15m' },
    { label: 'Last 1 hour', value: 'last-1h' },
    { label: 'Last 24 hours', value: 'last-24h' },
    { label: 'Last 7 days', value: 'last-7d' },
    { label: 'Last 30 days', value: 'last-30d' },
    { label: 'Custom range', value: 'custom' },
  ];
  
  // Toggle auto refresh
  const toggleAutoRefresh = (enabled: boolean) => {
    dispatch(discoverActions.setAutoRefresh(enabled));
  };
  
  // Set refresh interval
  const setRefreshInterval = (interval: number) => {
    dispatch(discoverActions.setRefreshInterval(interval));
  };
  
  // Manual refresh
  const refresh = () => {
    dispatch(discoverActions.refresh());
  };
  
  return {
    timeRange,
    histogramData,
    groupedData,
    timeInterval,
    setTimeRangePreset,
    setCustomTimeRange,
    formatDate,
    timeRangeOptions,
    toggleAutoRefresh,
    setRefreshInterval,
    refresh,
    autoRefresh: state.autoRefresh,
    refreshInterval: state.refreshInterval,
  };
};