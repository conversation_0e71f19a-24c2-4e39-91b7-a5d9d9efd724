/**
 * Performance monitoring hook for React components
 * 
 * This hook provides utilities for measuring and tracking performance
 * in React components.
 */

import { useEffect, useRef, useCallback } from 'react';
import { createPerformanceTracker } from '../utils/performanceTracking';
import { 
  createPerformanceComparisonTracker, 
  OperationCategory 
} from '../utils/performanceComparison';
import { createPerformanceLogger } from '../utils/performanceLogger';

/**
 * Hook for monitoring component performance
 * @param componentName Name of the component
 * @param discoverComponentName Optional name of the equivalent Discover component for comparison
 * @returns Object with performance monitoring utilities
 */
export function usePerformanceMonitoring(
  componentName: string,
  discoverComponentName?: string
) {
  // Create performance tracker
  const tracker = useRef(createPerformanceTracker(componentName));
  
  // Create comparison tracker if Discover component name is provided
  const comparisonTracker = useRef(
    discoverComponentName 
      ? createPerformanceComparisonTracker(componentName, discoverComponentName)
      : null
  );
  
  // Create performance logger
  const logger = useRef(createPerformanceLogger(componentName));
  
  // Track render time
  const renderStartTime = useRef(0);
  
  // Start tracking render time
  useEffect(() => {
    renderStartTime.current = performance.now();
    
    return () => {
      // Track component unmount time
      const unmountTime = performance.now() - renderStartTime.current;
      tracker.current.trackOperation('unmount', () => {}, 'lifecycle', { unmountTime });
    };
  }, []);
  
  // Track initial render time
  useEffect(() => {
    const renderTime = performance.now() - renderStartTime.current;
    tracker.current.trackOperation('initialRender', () => {}, 'lifecycle', { renderTime });
    
    // Compare with Discover component if available
    if (comparisonTracker.current && discoverComponentName) {
      comparisonTracker.current.compareOperation(
        'initialRender',
        'initialRender',
        OperationCategory.RENDERING
      );
    }
  }, [discoverComponentName]);
  
  // Track re-renders
  const renderCount = useRef(0);
  
  useEffect(() => {
    renderCount.current += 1;
    
    // Skip first render as it's tracked separately
    if (renderCount.current > 1) {
      const renderTime = performance.now() - renderStartTime.current;
      tracker.current.trackOperation('rerender', () => {}, 'lifecycle', { 
        renderTime,
        renderCount: renderCount.current 
      });
      
      // Compare with Discover component if available
      if (comparisonTracker.current && discoverComponentName) {
        comparisonTracker.current.compareOperation(
          'rerender',
          'rerender',
          OperationCategory.RENDERING
        );
      }
    }
    
    // Reset render start time for next render
    renderStartTime.current = performance.now();
  });
  
  // Track operation with timing
  const trackOperation = useCallback(<T>(
    operationName: string,
    fn: () => T,
    category?: string,
    compareWithDiscover: boolean = false
  ): T => {
    const result = tracker.current.trackOperation(operationName, fn, category);
    
    // Compare with Discover component if requested
    if (compareWithDiscover && comparisonTracker.current && discoverComponentName) {
      let operationCategory: OperationCategory;
      
      // Map category string to OperationCategory enum
      switch (category) {
        case 'filtering':
          operationCategory = OperationCategory.FILTERING;
          break;
        case 'sorting':
          operationCategory = OperationCategory.SORTING;
          break;
        case 'pagination':
          operationCategory = OperationCategory.PAGINATION;
          break;
        case 'interaction':
          operationCategory = OperationCategory.INTERACTION;
          break;
        case 'rendering':
          operationCategory = OperationCategory.RENDERING;
          break;
        default:
          operationCategory = OperationCategory.DATA_PROCESSING;
      }
      
      comparisonTracker.current.compareOperation(
        operationName,
        operationName,
        operationCategory
      );
    }
    
    return result;
  }, [discoverComponentName]);
  
  // Start timing an operation
  const startOperationTimer = useCallback((
    operationName: string
  ): string => {
    return tracker.current.startOperationTimer(operationName);
  }, []);
  
  // Stop timing an operation
  const stopOperationTimer = useCallback((
    operationName: string,
    category?: string,
    compareWithDiscover: boolean = false
  ): number => {
    const duration = tracker.current.stopOperationTimer(operationName, category);
    
    // Compare with Discover component if requested
    if (compareWithDiscover && comparisonTracker.current && discoverComponentName) {
      let operationCategory: OperationCategory;
      
      // Map category string to OperationCategory enum
      switch (category) {
        case 'filtering':
          operationCategory = OperationCategory.FILTERING;
          break;
        case 'sorting':
          operationCategory = OperationCategory.SORTING;
          break;
        case 'pagination':
          operationCategory = OperationCategory.PAGINATION;
          break;
        case 'interaction':
          operationCategory = OperationCategory.INTERACTION;
          break;
        case 'rendering':
          operationCategory = OperationCategory.RENDERING;
          break;
        default:
          operationCategory = OperationCategory.DATA_PROCESSING;
      }
      
      comparisonTracker.current.compareOperation(
        operationName,
        operationName,
        operationCategory
      );
    }
    
    return duration;
  }, [discoverComponentName]);
  
  // Log performance metrics
  const logPerformance = useCallback((detailed: boolean = false) => {
    logger.current.logComponentPerformance(detailed);
    
    if (comparisonTracker.current) {
      comparisonTracker.current.logComponentComparison();
    }
  }, []);
  
  return {
    trackOperation,
    startOperationTimer,
    stopOperationTimer,
    logPerformance,
    renderCount: renderCount.current
  };
}