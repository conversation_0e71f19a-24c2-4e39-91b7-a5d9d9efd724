/**
 * Types for Configuration Assessment feature
 */

/**
 * Represents a single configuration assessment check result
 */
export interface ConfigurationEntry {
  id: string;
  timestamp: Date;
  agent: {
    id: string;
    name: string;
    ip: string;
  };
  rule: {
    id: number;
    description: string;
    level: number;
    groups: string[];
  };
  check: {
    id: string;
    title: string;
    description: string;
    rationale: string;
    remediation: string;
    compliance: string[];
  };
  result: 'passed' | 'failed' | 'not_applicable';
  score: number;
  scan_id: string;
  component: string;
  configuration: string;
  data: Record<string, any>;
}

/**
 * Filter options specific to configuration assessment
 */
export interface ConfigurationFilter {
  field: keyof ConfigurationEntry | string;
  value: string | number | boolean | string[];
  operator: 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains' | 'in' | 'not_in';
}

/**
 * Sort options for configuration assessment
 */
export interface ConfigurationSort {
  field: keyof ConfigurationEntry | string;
  direction: 'asc' | 'desc';
}

/**
 * Configuration assessment search parameters
 */
export interface ConfigurationSearchParams {
  query?: string;
  filters?: ConfigurationFilter[];
  sort?: ConfigurationSort;
  timeRange?: {
    start: Date;
    end: Date;
  };
  page?: number;
  pageSize?: number;
}

/**
 * Result status for configuration assessment entries
 */
export type ConfigurationResultStatus = 'passed' | 'failed' | 'not_applicable';

/**
 * Compliance standard types
 */
export type ComplianceStandard = 'PCI DSS' | 'HIPAA' | 'NIST' | 'GDPR' | 'CIS' | 'SOC2' | 'ISO27001';

/**
 * Severity level for configuration issues
 */
export enum SeverityLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * Maps numeric score to severity level
 */
export interface SeverityMapping {
  min: number;
  max: number;
  level: SeverityLevel;
}