// Core data structures for the Discover dashboard

export interface LogEntry {
  id: string;
  timestamp: Date;
  source: string;
  message: string;
  level: 'info' | 'warning' | 'error' | 'critical';
  agent: {
    id: string;
    name: string;
    ip: string;
  };
  rule: {
    id: number;
    description: string;
    level: number;
    groups: string[];
  };
  location: string;
  decoder: {
    name: string;
  };
  data: Record<string, any>; // Additional fields
}

export interface Filter {
  field: string;
  operator: 'is' | 'is not' | 'exists' | 'does not exist' | 'contains';
  value: string | number | boolean;
  enabled: boolean;
}

export interface TimeRange {
  from: Date;
  to: Date;
  preset?: 'last-15m' | 'last-1h' | 'last-24h' | 'last-7d' | 'last-30d' | 'custom';
}

export interface HistogramBucket {
  timestamp: Date;
  count: number;
  interval: string;
}

export interface DiscoverState {
  searchQuery: string;
  timeRange: TimeRange;
  selectedFields: string[];
  appliedFilters: Filter[];
  logData: LogEntry[];
  filteredData: LogEntry[];
  histogramData: HistogramBucket[];
  isLoading: boolean;
  autoRefresh: boolean;
  refreshInterval: number;
  pagination: {
    currentPage: number;
    pageSize: number;
    totalItems: number;
  };
}

export type DiscoverAction =
  | { type: 'SET_SEARCH_QUERY'; payload: string }
  | { type: 'SET_TIME_RANGE'; payload: TimeRange }
  | { type: 'ADD_FILTER'; payload: Filter }
  | { type: 'REMOVE_FILTER'; payload: string }
  | { type: 'TOGGLE_FIELD'; payload: string }
  | { type: 'SET_LOG_DATA'; payload: LogEntry[] }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_AUTO_REFRESH'; payload: boolean }
  | { type: 'SET_REFRESH_INTERVAL'; payload: number }
  | { type: 'SET_CURRENT_PAGE'; payload: number }
  | { type: 'SET_PAGE_SIZE'; payload: number };