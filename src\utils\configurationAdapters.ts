/**
 * Configuration Assessment Adapters
 * 
 * This file contains adapter functions to map configuration assessment data
 * to the format expected by Discover components.
 * 
 * Requirements: 1.2, 1.3, 6.3
 */

import { 
  ConfigurationEntry, 
  ConfigurationFilter, 
  ConfigurationSort,
  SeverityLevel
} from '../types/configuration';
import { getSeverityFromScore } from './configurationUtils';

/**
 * Field mappings for configuration assessment data
 * Maps configuration fields to display names and formatting functions
 */
export const configurationFieldMappings = {
  'rule.id': {
    displayName: 'Rule ID',
    format: (value: number) => value.toString(),
    width: '100px',
  },
  'rule.description': {
    displayName: 'Rule Description',
    format: (value: string) => value,
    width: '250px',
  },
  'check.title': {
    displayName: 'Check Title',
    format: (value: string) => value,
    width: '200px',
  },
  'check.description': {
    displayName: 'Check Description',
    format: (value: string) => value,
    width: '300px',
  },
  'check.remediation': {
    displayName: 'Remediation',
    format: (value: string) => value,
    width: '300px',
  },
  'result': {
    displayName: 'Result',
    format: (value: string) => value.charAt(0).toUpperCase() + value.slice(1).replace('_', ' '),
    width: '120px',
    getColor: (value: string) => {
      switch (value) {
        case 'passed':
          return '#4CAF50'; // Green
        case 'failed':
          return '#F44336'; // Red
        case 'not_applicable':
          return '#9E9E9E'; // Gray
        default:
          return 'inherit';
      }
    }
  },
  'score': {
    displayName: 'Score',
    format: (value: number) => value.toFixed(1),
    width: '80px',
    getColor: (value: number) => {
      const severity = getSeverityFromScore(value);
      switch (severity) {
        case SeverityLevel.LOW:
          return '#4CAF50'; // Green
        case SeverityLevel.MEDIUM:
          return '#FF9800'; // Orange
        case SeverityLevel.HIGH:
          return '#F44336'; // Red
        case SeverityLevel.CRITICAL:
          return '#9C27B0'; // Purple
        default:
          return 'inherit';
      }
    }
  },
  'component': {
    displayName: 'Component',
    format: (value: string) => value,
    width: '150px',
  },
  'agent.name': {
    displayName: 'Agent',
    format: (value: string) => value,
    width: '150px',
  },
  'agent.ip': {
    displayName: 'Agent IP',
    format: (value: string) => value,
    width: '120px',
  },
  'timestamp': {
    displayName: 'Timestamp',
    format: (value: Date) => value.toLocaleString(),
    width: '180px',
  },
  'check.compliance': {
    displayName: 'Compliance',
    format: (value: string[]) => value.join(', '),
    width: '200px',
  },
  'configuration': {
    displayName: 'Configuration',
    format: (value: string) => value,
    width: '150px',
  }
};

/**
 * Default fields to display in the configuration table
 */
export const defaultConfigurationFields = [
  'rule.id',
  'rule.description',
  'check.title',
  'result',
  'score',
  'component',
  'agent.name',
  'timestamp'
];

/**
 * Get field value from configuration entry using dot notation
 * @param entry Configuration entry
 * @param fieldPath Field path with dot notation
 * @returns Field value
 */
export const getFieldValue = (entry: ConfigurationEntry, fieldPath: string): any => {
  const parts = fieldPath.split('.');
  let value: any = entry;

  for (const part of parts) {
    if (value === undefined || value === null) {
      return undefined;
    }
    value = value[part];
  }

  return value;
};

/**
 * Format field value for display
 * @param value Field value
 * @param fieldPath Field path
 * @returns Formatted value
 */
export const formatFieldValue = (value: any, fieldPath: string): string => {
  if (value === undefined || value === null) {
    return '-';
  }

  const mapping = configurationFieldMappings[fieldPath as keyof typeof configurationFieldMappings];
  if (mapping && mapping.format) {
    return mapping.format(value);
  }

  if (value instanceof Date) {
    return value.toLocaleString();
  }
  
  if (Array.isArray(value)) {
    return value.join(', ');
  }
  
  if (typeof value === 'object') {
    return JSON.stringify(value);
  }
  
  return String(value);
};

/**
 * Get color for field value
 * @param value Field value
 * @param fieldPath Field path
 * @returns Color string or undefined
 */
export const getFieldColor = (value: any, fieldPath: string): string | undefined => {
  const mapping = configurationFieldMappings[fieldPath as keyof typeof configurationFieldMappings];
  if (mapping && mapping.getColor) {
    return mapping.getColor(value);
  }
  return undefined;
};

/**
 * Convert configuration filter to discover filter format
 * @param filter Configuration filter
 * @returns Discover filter format
 */
export const configFilterToDiscoverFilter = (filter: ConfigurationFilter): any => {
  // Map configuration filter operators to discover filter operators
  const operatorMap: Record<string, string> = {
    'eq': 'is',
    'neq': 'is not',
    'contains': 'contains',
    'in': 'is',
    'not_in': 'is not'
  };

  return {
    field: filter.field.toString(),
    operator: operatorMap[filter.operator] || 'is',
    value: filter.value,
    enabled: true
  };
};

/**
 * Convert discover filter format to configuration filter
 * @param filter Discover filter format
 * @returns Configuration filter
 */
export const discoverFilterToConfigFilter = (filter: any): ConfigurationFilter => {
  // Map discover filter operators to configuration filter operators
  const operatorMap: Record<string, any> = {
    'is': 'eq',
    'is not': 'neq',
    'contains': 'contains',
    'exists': 'in',
    'does not exist': 'not_in'
  };

  return {
    field: filter.field,
    operator: operatorMap[filter.operator] || 'eq',
    value: filter.value
  };
};

/**
 * Convert configuration sort to discover sort format
 * @param sort Configuration sort
 * @returns Discover sort format
 */
export const configSortToDiscoverSort = (sort: ConfigurationSort): any => {
  return {
    field: sort.field.toString(),
    direction: sort.direction
  };
};

/**
 * Convert discover sort format to configuration sort
 * @param sort Discover sort format
 * @returns Configuration sort
 */
export const discoverSortToConfigSort = (sort: any): ConfigurationSort => {
  return {
    field: sort.field,
    direction: sort.direction as 'asc' | 'desc'
  };
};

/**
 * Get available fields from configuration data
 * @param entries Configuration entries
 * @returns Record of field names to field metadata
 */
export const getAvailableConfigurationFields = (entries: ConfigurationEntry[]): Record<string, any> => {
  const fieldStats: Record<string, any> = {};

  // Add fields from field mappings
  Object.keys(configurationFieldMappings).forEach(field => {
    const mapping = configurationFieldMappings[field as keyof typeof configurationFieldMappings];
    fieldStats[field] = {
      type: getFieldType(entries[0] ? getFieldValue(entries[0], field) : null),
      displayName: mapping.displayName,
      count: entries.filter(entry => getFieldValue(entry, field) !== undefined).length,
      values: getTopFieldValues(entries, field),
    };
  });

  return fieldStats;
};

/**
 * Get field type from sample value
 * @param value Sample value
 * @returns Field type
 */
export const getFieldType = (value: any): string => {
  if (value === null || value === undefined) {
    return 'unknown';
  }
  
  if (value instanceof Date) {
    return 'date';
  }
  
  if (Array.isArray(value)) {
    return 'array';
  }
  
  if (typeof value === 'object') {
    return 'object';
  }
  
  return typeof value;
};

/**
 * Get top values for a specific field
 * @param entries Configuration entries
 * @param fieldPath Field path
 * @param limit Maximum number of values to return
 * @returns Array of value and count pairs
 */
export const getTopFieldValues = (
  entries: ConfigurationEntry[],
  fieldPath: string,
  limit: number = 10
): Array<{ value: any; count: number }> => {
  const valueCounts: Record<string, number> = {};

  entries.forEach(entry => {
    const value = getFieldValue(entry, fieldPath);
    if (value !== undefined && value !== null) {
      const key = String(value);
      valueCounts[key] = (valueCounts[key] || 0) + 1;
    }
  });

  const results: Array<{ value: any; count: number }> = [];
  for (const key in valueCounts) {
    if (valueCounts.hasOwnProperty(key)) {
      results.push({ value: key, count: valueCounts[key] });
    }
  }
  
  return results
    .sort((a, b) => b.count - a.count)
    .slice(0, limit);
};

/**
 * Group configuration entries by field
 * @param entries Configuration entries
 * @param field Field to group by
 * @returns Record of field values to entries
 */
export const groupConfigurationEntriesByField = (
  entries: ConfigurationEntry[],
  field: string
): Record<string, ConfigurationEntry[]> => {
  const groups: Record<string, ConfigurationEntry[]> = {};

  entries.forEach(entry => {
    const value = getFieldValue(entry, field);
    if (value !== undefined && value !== null) {
      const key = String(value);
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(entry);
    }
  });

  return groups;
};

/**
 * Calculate distribution of configuration results
 * @param entries Configuration entries
 * @returns Record of result values to counts
 */
export const calculateResultDistribution = (
  entries: ConfigurationEntry[]
): Record<string, number> => {
  const distribution: Record<string, number> = {
    passed: 0,
    failed: 0,
    not_applicable: 0
  };

  entries.forEach(entry => {
    distribution[entry.result]++;
  });

  return distribution;
};

/**
 * Calculate distribution of compliance standards
 * @param entries Configuration entries
 * @returns Record of compliance standard to count
 */
export const calculateComplianceDistribution = (
  entries: ConfigurationEntry[]
): Record<string, number> => {
  const distribution: Record<string, number> = {};

  entries.forEach(entry => {
    entry.check.compliance.forEach(standard => {
      distribution[standard] = (distribution[standard] || 0) + 1;
    });
  });

  return distribution;
};

/**
 * Calculate distribution of severity levels
 * @param entries Configuration entries
 * @returns Record of severity level to count
 */
export const calculateSeverityDistribution = (
  entries: ConfigurationEntry[]
): Record<string, number> => {
  const distribution: Record<string, number> = {
    [SeverityLevel.LOW]: 0,
    [SeverityLevel.MEDIUM]: 0,
    [SeverityLevel.HIGH]: 0,
    [SeverityLevel.CRITICAL]: 0
  };

  entries.forEach(entry => {
    const severity = getSeverityFromScore(entry.score);
    distribution[severity]++;
  });

  return distribution;
};

/**
 * Format configuration entry for display in table
 * @param entry Configuration entry
 * @param selectedFields Fields to include
 * @returns Formatted entry for display
 */
export const formatConfigurationEntryForTable = (
  entry: ConfigurationEntry,
  selectedFields: string[] = defaultConfigurationFields
): Record<string, any> => {
  const formattedEntry: Record<string, any> = { id: entry.id };

  selectedFields.forEach(field => {
    const value = getFieldValue(entry, field);
    formattedEntry[field] = {
      raw: value,
      formatted: formatFieldValue(value, field),
      color: getFieldColor(value, field)
    };
  });

  return formattedEntry;
};

/**
 * Format configuration entry for details view
 * @param entry Configuration entry
 * @returns Formatted entry for details view
 */
export const formatConfigurationEntryForDetails = (
  entry: ConfigurationEntry
): Record<string, any> => {
  return {
    id: entry.id,
    timestamp: entry.timestamp,
    agent: {
      id: entry.agent.id,
      name: entry.agent.name,
      ip: entry.agent.ip
    },
    rule: {
      id: entry.rule.id,
      description: entry.rule.description,
      level: entry.rule.level,
      groups: entry.rule.groups
    },
    check: {
      id: entry.check.id,
      title: entry.check.title,
      description: entry.check.description,
      rationale: entry.check.rationale,
      remediation: entry.check.remediation,
      compliance: entry.check.compliance
    },
    result: entry.result,
    score: entry.score,
    severity: getSeverityFromScore(entry.score),
    scan_id: entry.scan_id,
    component: entry.component,
    configuration: entry.configuration,
    data: entry.data
  };
};
